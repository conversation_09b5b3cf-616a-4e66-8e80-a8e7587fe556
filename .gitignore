.DS_Store
node_modules
dist

# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.terraform
terraform.exe
.common/
.terraform.lock.hcl
errored.tfstate
.lambdabuild/
tools/sql_out/
tools/data/

eslintcache
.prettiercache/
coverage/
.windsurf/
jest-html-reporters-*.html
.cursor/
*.log
*.pem
.lambdabuild/
.aws-sam/
.idea/
stats.html
.prettierrc
**/*-instructions.md
