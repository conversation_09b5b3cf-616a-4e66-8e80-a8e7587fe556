@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *商品一覧ページ
 * *********************************************************************** */
.list #main #list-head .container {
  padding: 0 4vw 4vw;
}

/*** オークションタイプ表記 ***/
.auction-type-label {
  margin: 0;
  padding: 1rem;
  text-align: center;
  background-color: #000;
}
@media screen and (max-width: 767px) {
  .auction-type-label {
    padding: 4vw;
  }
}
.auction-type-label .type-tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 1rem;
  width: 600px;
  margin: 0 auto;
  padding: 3px;
  background-color: #3f3f3f;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  .auction-type-label .type-tab-wrap {
    width: 100%;
    gap: 1vw;
  }
}
.auction-type-label .type-tab-wrap span, .auction-type-label .type-tab-wrap a {
  display: inline-block;
  padding: 0.6rem 1.5rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 50px;
  line-height: 1.2;
  text-align: center;
}
@media screen and (max-width: 767px) {
  .auction-type-label .type-tab-wrap span, .auction-type-label .type-tab-wrap a {
    padding: 2vw;
    font-size: 2.8vw;
  }
}
.auction-type-label .type-tab-wrap a {
  width: calc(50% - 0.5rem);
  margin: 0 1rem;
}
@media screen and (max-width: 767px) {
  .auction-type-label .type-tab-wrap a {
    width: calc(50% - 0.5vw);
    margin: 0;
  }
}
.auction-type-label .type-tab-wrap span {
  width: calc(50% - 0.5rem);
  padding-left: 50px;
  color: #4194d3;
  line-height: 1;
  cursor: default;
  background-color: #f5f5f5;
  background-image: url(../img/common/icn_check_l-blue.svg);
  background-size: 16px auto;
  background-repeat: no-repeat;
  background-position: 30px 50%;
  z-index: 1;
}
@media screen and (max-width: 767px) {
  .auction-type-label .type-tab-wrap span {
    width: calc(50% - 0.5vw);
    padding-left: 4vw;
    background-size: 2.5vw auto;
    background-position: 3vw 50%;
  }
}

/*** リストタイプ表記 ***/
.list-type-label {
  margin: 0.5rem 0;
  padding: 1rem 1rem;
  color: #fff;
  font-size: 0.9rem;
  background-color: #333;
}
.list-type-label span {
  display: inline-block;
  font-weight: 600;
  border-radius: 4px;
}
.list-type-label span:after {
  content: ":";
  display: inline-block;
  margin: 0 1rem;
}

#main h2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  height: auto;
}
#main h2::after {
  content: "";
  display: block;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
#main #list-head {
  margin: 0;
  padding: 0;
}
#main #list-head .search-panel-wrap {
  padding: 24px 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap {
    padding: 0;
  }
}
#main #list-head .search-panel-wrap .head {
  padding: 1rem 1rem 0;
  font-size: 1.4rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .head {
    padding: 4vw 4vw 0;
    font-size: 4.5vw;
  }
}
#main #list-head .search-panel-wrap .contents {
  padding: 1.5rem 0 2rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents {
    padding: 1rem 1rem 2rem;
  }
}
#main #list-head .search-panel-wrap .contents .keyword {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .keyword {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0 0.5rem 1.5rem;
  }
}
#main #list-head .search-panel-wrap .contents .keyword__label {
  width: 180px;
  padding: 0.3rem 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .keyword__label {
    width: 100%;
    font-size: 3.5vw;
    padding: 1vw 0;
  }
}
#main #list-head .search-panel-wrap .contents .keyword input {
  width: calc(100% - 180px);
  max-width: 700px;
  margin: 0 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .keyword input {
    width: 100%;
    margin: 0;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #e9eaeb;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0.5rem 0.5rem 1rem;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__label, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__label, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__label {
  width: 180px;
  padding: 0.3rem 1rem;
  font-weight: 500;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__label, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__label, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__label {
    width: 100%;
    padding: 1.7vw 0;
    font-size: 3.5vw;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents {
    width: 100%;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 2px;
  padding: 2px 5px;
  background-color: #efefef;
  border: 1px solid #e9eaeb;
  border-radius: 4px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item {
    margin: 0.3rem 0.3rem 0 0;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item:hover, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item:hover, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item:hover {
  background-color: transparent;
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item input, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item input, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item input {
  -webkit-transform: scale(1.3);
          transform: scale(1.3);
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item input, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item input, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item input {
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item input:focus, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item input:focus-visible, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item input:focus, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item input:focus-visible, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item input:focus, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item input:focus-visible {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item span, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item span, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item span, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item span, #main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item span {
    font-size: 3vw;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item {
  padding: 5px 12px;
  background-color: #f8f8f8;
  border: 1px solid #f8f8f8;
  -webkit-box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item:has(input:checked) {
  background-color: #ebf6ff;
  border: 1px solid #ebf6ff;
  -webkit-box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item input {
  display: block;
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-primary .model__contents .label-item input:checked ~ span {
  color: #1e71b2;
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item:has(input:checked) {
  background-color: #fffeee;
  border: 1px solid #fffeee;
  -webkit-box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-secondary .model__contents .label-item input:checked ~ span {
  color: #1e71b2;
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item:has(input:checked) {
  background-color: #f6fff1;
  border: 1px solid #f6fff1;
  -webkit-box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}
#main #list-head .search-panel-wrap .contents .category-wrap .s-category-tertiary .model__contents .label-item input:checked ~ span {
  color: #1e71b2;
}
#main #list-head .search-panel-wrap .contents .category-wrap [class^=s-category-]:first-child {
  border-top: 1px solid #e9eaeb;
}
#main #list-head .search-panel-wrap .contents .category-wrap .model {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #e9eaeb;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .model {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0.5rem 0.5rem 1rem;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .model .model__label {
  width: 180px;
  padding: 0.3rem 1rem;
  font-weight: 500;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .model .model__label {
    width: 100%;
    padding: 1.7vw 0;
    font-size: 3.5vw;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents {
    width: 100%;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents .label-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 0.15rem 0.3rem 0.1rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents .label-item {
    margin: 0.3rem 0.5rem 0 0;
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents .label-item input {
  -webkit-transform: scale(1.3);
          transform: scale(1.3);
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents .label-item input {
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
  }
}
#main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents .label-item label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .category-wrap .model .model__contents .label-item label {
    font-size: 3vw;
  }
}
#main #list-head .search-panel-wrap .contents .wrap-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 40px;
  margin: 0 auto;
  padding: 2rem 1rem 0;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .wrap-btn {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .wrap-btn button {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .wrap-btn a {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
}
#main #list-head .search-panel-wrap .contents .wrap-btn button, #main #list-head .search-panel-wrap .contents .wrap-btn a {
  cursor: pointer;
}
#main #list-head .search-panel-wrap .contents .wrap-btn button:hover, #main #list-head .search-panel-wrap .contents .wrap-btn a:hover {
  opacity: 0.8;
}
#main #list-head .search-panel-wrap .contents .wrap-btn button.clear, #main #list-head .search-panel-wrap .contents .wrap-btn a.clear {
  color: #427fae;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .wrap-btn button.clear, #main #list-head .search-panel-wrap .contents .wrap-btn a.clear {
    font-size: 3.2vw;
  }
}
#main #list-head .search-panel-wrap .contents .wrap-btn button.clear:hover, #main #list-head .search-panel-wrap .contents .wrap-btn a.clear:hover {
  opacity: 1;
  text-decoration: underline;
}
#main #list-head .search-panel-wrap .contents .wrap-btn button.search, #main #list-head .search-panel-wrap .contents .wrap-btn a.search {
  width: 240px;
  height: 55px;
  margin: 0;
  padding: 0.5rem 1rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  background-color: #427fae;
  border-radius: 30px;
}
@media screen and (max-width: 767px) {
  #main #list-head .search-panel-wrap .contents .wrap-btn button.search, #main #list-head .search-panel-wrap .contents .wrap-btn a.search {
    width: 60vw;
    height: 12vw;
    font-size: 3.8vw;
  }
}
#main #list-head .conditions {
  margin: 1rem auto 0;
  padding: 20px 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions {
    padding: 4vw 4vw;
  }
}
#main #list-head .conditions .conditions__label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0.4rem 1rem 1rem;
  border-bottom: 1px solid #e8e8e8;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .conditions__label {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0 0.5rem 1rem;
  }
}
#main #list-head .conditions .conditions__label .ttl {
  width: 100px;
  padding: 0.2rem 0;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .conditions__label .ttl {
    width: 100%;
    margin: 0 0 1vw;
    font-size: 4vw;
    text-align: left;
  }
}
#main #list-head .conditions .conditions__label .elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .conditions__label .elm {
    font-size: 3.5vw;
  }
}
#main #list-head .conditions .conditions__label .elm span {
  display: inline-block;
  margin: 0 0.5rem 0.5rem 0;
  padding: 0.4rem 1.5rem;
  line-height: 1;
  background-color: #fff;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .conditions__label .elm span {
    padding: 1.5vw 4vw;
    font-size: 3vw;
  }
}
#main #list-head .conditions .results__label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  padding: 1.2rem 1rem 0.2rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .results__label {
    padding: 1rem 0.5rem 0.2rem;
  }
}
#main #list-head .conditions .results__label .ttl {
  width: 100px;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .results__label .ttl {
    width: auto;
    padding: 0 4vw 0 0;
    font-size: 4vw;
  }
}
#main #list-head .conditions .results__label .elm {
  font-size: 1.4rem;
  font-weight: 700;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .results__label .elm {
    font-size: 5vw;
  }
}
#main #list-head .conditions .results__label .elm span {
  display: inline-block;
  margin-left: 0.2rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #list-head .conditions .results__label .elm span {
    margin-left: 1vw;
    font-size: 3.2vw;
  }
}
#main #list {
  width: calc(1280px + 2rem);
  max-width: 100%;
  margin: 0 auto;
  padding: 0 0 20px;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main #list {
    padding: 0 0 7vw;
  }
}
#main #list .container .bid-tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: end;
  gap: 3px;
  width: 100%;
  margin: 100px 0 40px;
  border-bottom: 1px solid #333;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap {
    margin: 10vw 0 4vw;
  }
}
#main #list .container .bid-tab-wrap .tab-cont, #main #list .container .bid-tab-wrap .tab {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 380px;
  max-width: 50%;
  margin: 0;
  padding: 0;
  border: 1px solid;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap .tab-cont, #main #list .container .bid-tab-wrap .tab {
    height: 14vw;
  }
}
#main #list .container .bid-tab-wrap .tab-cont {
  border-color: #333;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom: none;
  height: 62px;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap .tab-cont {
    max-width: 50%;
    height: 14vw;
    margin: 0;
  }
}
#main #list .container .bid-tab-wrap .tab-cont .label {
  display: inline-block;
  padding: 5px 0 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap .tab-cont .label {
    padding: 1vw 0 0;
    font-size: 3.5vw;
  }
}
#main #list .container .bid-tab-wrap .tab-cont:after {
  position: absolute;
  content: "";
  bottom: -2px;
  width: 100%;
  height: 3px;
  background-color: #fff;
  z-index: 1;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap .tab-cont:after {
    bottom: -1vw;
    height: 2vw;
  }
}
#main #list .container .bid-tab-wrap a.tab-cont {
  border-color: #c9c9c9;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom: none;
  height: 56px;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap a.tab-cont {
    height: 12vw;
  }
}
#main #list .container .bid-tab-wrap a.tab-cont .label {
  display: inline-block;
  padding: 2px 0 0;
  color: #c9c9c9;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #list .container .bid-tab-wrap a.tab-cont .label {
    padding: 1vw 0 0;
    font-size: 3.5vw;
  }
}
#main #list .container .bid-tab-wrap a.tab-cont:after {
  display: none;
}
#main #list .container .bid-tab-wrap a.tab-cont:hover {
  opacity: 1;
  border-color: #333;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
#main #list .container .bid-tab-wrap a.tab-cont:hover .label {
  color: #333;
}
#main #list .container .bid-tab-wrap a.tab-cont.active {
  z-index: 1;
  color: #fff;
  font-weight: 600;
  border-color: #333;
  cursor: default;
}
#main #list .container .bid-tab-wrap a.tab-cont.active .label {
  color: #333;
}
#main #list .container .display-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 0;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #list .container .display-option .refine {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: end;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  width: auto;
  padding: 1rem 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    width: 100%;
  }
}
#main #list .container .display-option .refine .sorting button.menu_trigger {
  position: relative;
  width: auto;
  min-width: 125px;
  margin: 0 2rem 0 0;
  padding: 0.2rem 1.5rem 0.2rem 1.2rem;
  font-size: 1rem;
  text-align: left;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine .sorting button.menu_trigger {
    padding: 2vw 4vw 2vw 0;
  }
}
#main #list .container .display-option .refine .sorting button.menu_trigger:after {
  content: "";
  position: absolute;
  right: 3px;
  top: calc(50% - 0px);
  width: 4px;
  height: 4px;
  border-right: 2px solid #bcbcbc;
  border-bottom: 2px solid #bcbcbc;
  -webkit-transform: translateY(-50%) rotate(45deg);
          transform: translateY(-50%) rotate(45deg);
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine .sorting button.menu_trigger .option_selected {
    font-size: 3.8vw;
  }
}
#main #list .container .display-option .refine .sorting ul.sorting_panel {
  display: none;
  position: absolute;
  top: 55px;
  left: 0;
  width: 200px;
  max-width: calc(100% - 2rem);
  margin: 0;
  padding: 0.8rem 0 1rem;
  z-index: 50;
  background-color: #fff;
  border: 1ps solid #eee;
  border-radius: 8px;
  -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
          box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
}
#main #list .container .display-option .refine .sorting ul.sorting_panel.is-active {
  display: block;
}
#main #list .container .display-option .refine .sorting ul.sorting_panel .option_item {
  margin: 0;
  padding: 0.2rem 1.2rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine .sorting ul.sorting_panel .option_item {
    font-size: 3.8vw;
  }
}
#main #list .container .display-option .refine .sorting ul.sorting_panel .option_item:hover {
  background-color: #eee;
}
#main #list .container .display-option .refine .sorting ul.sorting_panel .option_item a {
  cursor: pointer;
}
#main #list .container .display-option .refine .check-onsale {
  width: auto;
  margin: 0;
  padding: 0.2rem 1.2rem;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine .check-onsale {
    padding: 2vw 0;
  }
}
#main #list .container .display-option .refine .check-onsale input {
  margin: 0;
  -webkit-transform: scale(1.3) translateY(2px);
          transform: scale(1.3) translateY(2px);
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine .check-onsale input {
    -webkit-transform: scale(1.8) translateY(0.2vw);
            transform: scale(1.8) translateY(0.2vw);
  }
}
#main #list .container .display-option .refine .check-onsale label {
  display: inline-block;
  margin: 0 0.7rem;
  white-space: nowrap;
  -webkit-transform: translateY(2px);
          transform: translateY(2px);
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .refine .check-onsale label {
    font-size: 3.8vw;
    margin: 0 0 0 4vw;
  }
}
#main #list .container .display-option .refine .count p {
  font-weight: 500;
}
#main #list .container .display-option .refine .count p span {
  display: inline-block;
  margin: 0 0.2rem 0 0;
  font-size: 1.4rem;
  font-weight: 600;
}
#main #list .container .display-option .switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  width: auto;
  padding: 1rem 0;
}
@media screen and (max-width: 1080px) {
  #main #list .container .display-option .switch {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch {
    width: 100%;
    padding: 4vw 0;
    border-top: 1px solid #ccc;
  }
}
#main #list .container .display-option .switch .dl {
  padding: 0.2rem 1rem;
}
@media screen and (max-width: 1080px) {
  #main #list .container .display-option .switch .dl {
    width: 100%;
    text-align: right;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .dl {
    margin: 0 0 4vw;
    padding: 0 0 4.5vw;
    border-bottom: 1px solid #ccc;
  }
}
#main #list .container .display-option .switch .dl a {
  text-decoration: underline;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .dl a span {
    font-size: 3.8vw;
  }
}
#main #list .container .display-option .switch .dl a:hover {
  text-decoration: none;
}
#main #list .container .display-option .switch .number-switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.2rem 1rem;
  border-left: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .number-switch {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    padding: 2vw 0;
    border-left: none;
  }
}
#main #list .container .display-option .switch .number-switch .label {
  margin: 0 1rem 0 0;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .number-switch .label {
    font-size: 3.8vw;
  }
}
#main #list .container .display-option .switch .number-switch .num .btn {
  display: inline-block;
  margin: 0 0.5rem;
  padding: 0;
  color: #c9c9c9;
  font-size: 1rem;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .number-switch .num .btn {
    font-size: 3.8vw;
  }
}
#main #list .container .display-option .switch .number-switch .num .btn.is-active {
  color: #000;
  text-decoration: underline;
}
#main #list .container .display-option .switch .number-switch .num .btn:hover {
  color: #000;
  text-decoration: underline;
}
#main #list .container .display-option .switch .display-switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.2rem 1rem;
  border-left: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch {
    padding: 2vw 0 2vw 4vw;
  }
}
#main #list .container .display-option .switch .display-switch p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 0.6rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p {
    margin: 0 0.6rem;
  }
}
#main #list .container .display-option .switch .display-switch p .btn {
  width: 21px;
  height: 21px;
  padding: 0;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn {
    width: 6vw;
    height: 6vw;
  }
}
#main #list .container .display-option .switch .display-switch p .btn.row {
  background: url("../img/common/icn_list_switch_row_off.svg") no-repeat;
  background-size: 21px 21px;
  background-position: center;
  background-clip: content-box;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn.row {
    background-size: 6vw 6vw;
  }
}
#main #list .container .display-option .switch .display-switch p .btn.row.is-active {
  background: url("../img/common/icn_list_switch_row_on.svg") no-repeat;
  cursor: default;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn.row.is-active {
    background-size: 6vw 6vw;
  }
}
#main #list .container .display-option .switch .display-switch p .btn.row:hover {
  background: url("../img/common/icn_list_switch_row_on.svg") no-repeat;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn.row:hover {
    background-size: 6vw 6vw;
  }
}
#main #list .container .display-option .switch .display-switch p .btn.panel {
  background: url("../img/common/icn_list_switch_panel_off.svg") no-repeat;
  background-size: 21px 21px;
  background-position: center;
  background-clip: content-box;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn.panel {
    background-size: 6vw 6vw;
  }
}
#main #list .container .display-option .switch .display-switch p .btn.panel.is-active {
  background: url("../img/common/icn_list_switch_panel_on.svg") no-repeat;
  cursor: default;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn.panel.is-active {
    background-size: 6vw auto;
  }
}
#main #list .container .display-option .switch .display-switch p .btn.panel:hover {
  background: url("../img/common/icn_list_switch_panel_on.svg") no-repeat;
}
@media screen and (max-width: 767px) {
  #main #list .container .display-option .switch .display-switch p .btn.panel:hover {
    background-size: 6vw auto;
  }
}
#main #list .container .wrap-btn {
  width: 100%;
  margin: 2rem 0 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #list .container .wrap-btn {
    margin: 10vw 0 0;
  }
}
#main #list .container .wrap-btn .list-more {
  width: 300px;
  height: 55px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #bf2a24;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #list .container .wrap-btn .list-more {
    height: 60px;
  }
}
#main #list .container .item-list.row {
  margin: 0;
  text-align: center;
}
#main #list .container .item-list.row > ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 0;
  width: 100%;
}
#main #list .container .item-list.row > ul > li {
  position: relative;
  width: 100%;
  padding: 0;
  margin: 0;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li {
    margin: 0 0 1.5 rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #list .container .item-list.row > ul > li + li {
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li + li {
    margin: 0;
  }
}
#main #list .container .item-list.row > ul > li:last-child {
  border-bottom: 1px solid #ccc;
}
#main #list .container .item-list.row > ul > li.soldout {
  position: relative;
}
#main #list .container .item-list.row > ul > li.soldout figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/layer_soldout.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.row > ul > li.soldout .place-bid:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 10;
}
#main #list .container .item-list.row > ul > li.nego {
  position: relative;
}
#main #list .container .item-list.row > ul > li.nego figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/icn_nego.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.row > ul > li.nego .place-bid:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 10;
}
#main #list .container .item-list.row > ul > li a {
  position: relative;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 150px 16px 1fr;
  grid-template-columns: 150px 1fr;
  -ms-grid-rows: auto 0 auto 0 auto 0 auto 0 auto;
  grid-template-rows: repeat(5, auto);
  gap: 0 16px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: start;
  width: 100%;
  margin: 0;
  padding: 1rem 0;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(1) {
  -ms-grid-row: 1;
  -ms-grid-column: 1;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(2) {
  -ms-grid-row: 1;
  -ms-grid-column: 3;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(3) {
  -ms-grid-row: 3;
  -ms-grid-column: 1;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(4) {
  -ms-grid-row: 3;
  -ms-grid-column: 3;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(5) {
  -ms-grid-row: 5;
  -ms-grid-column: 1;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(6) {
  -ms-grid-row: 5;
  -ms-grid-column: 3;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(7) {
  -ms-grid-row: 7;
  -ms-grid-column: 1;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(8) {
  -ms-grid-row: 7;
  -ms-grid-column: 3;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(9) {
  -ms-grid-row: 9;
  -ms-grid-column: 1;
}
#main #list .container .item-list.row > ul > li a > *:nth-child(10) {
  -ms-grid-row: 9;
  -ms-grid-column: 3;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a {
    -ms-grid-columns: 20vw 4vw 1fr;
    grid-template-columns: 20vw 1fr;
    gap: 0 4vw;
    padding: 4vw 0;
  }
}
#main #list .container .item-list.row > ul > li a:hover {
  background-color: #f8f8f8;
}
#main #list .container .item-list.row > ul > li a figure {
  position: relative;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  -ms-grid-row: 1;
  -ms-grid-row-span: 2;
  grid-row: 1/3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 150px;
  height: 150px;
  border: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a figure {
    width: 20vw;
    height: 20vw;
  }
}
#main #list .container .item-list.row > ul > li a figure img {
  display: block;
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
#main #list .container .item-list.row > ul > li a figure .tab-f span {
  height: 20px;
  padding: 0 14px;
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a figure .tab-f span {
    height: 4vw;
    padding: 0 2vw;
    font-size: 2vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc {
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc {
    padding: 0 0 2vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .item-name {
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
  width: 100%;
  margin: 0;
  padding: 0 0.5rem 0.5rem 0;
  color: #427fae;
  font-weight: 500;
  font-size: 0.9rem;
  line-height: 1.4;
  border-bottom: 1px solid #e9eaeb;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .item-name {
    margin: 0;
    padding: 0 0 2vw;
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .item-name .name-title {
  margin: 0 0.5rem 0.2rem 0;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .item-name .name-title {
    margin: 0 2vw 1vw 0;
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .item-name .tab-item {
  display: inline-block;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0;
  padding: 0 0.7rem;
  color: #333;
  font-size: 0.65rem;
  font-weight: 600;
  line-height: 1.1rem;
  border: 1px solid #333;
  border-radius: 2px;
  -webkit-transform: translateY(-0.1rem);
          transform: translateY(-0.1rem);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .item-name .tab-item {
    font-size: 2.5vw;
    line-height: 3.8vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .current-price {
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  -ms-grid-row: 2;
  -ms-grid-row-span: 1;
  grid-row: 2/3;
  padding: 0;
  line-height: 1.2rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .current-price {
    margin: 0 0 0.5vw;
    font-size: 3.6vw;
    line-height: 5.8vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .current-price .price-c {
  display: inline-block;
  margin-right: 5px;
  color: #231914;
  font-size: 0.8rem;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .current-price .price-c {
    font-size: 3vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .current-price .price-v {
  color: #E50A09;
  font-size: 1.2rem;
  font-weight: 700;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .current-price .price-v {
    font-size: 5.8vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .current-price .price-u {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .current-price .price-u {
    font-size: 3vw;
  }
}
#main #list .container .item-list.row > ul > li a .panel-disc .current-price .tax-u {
  display: inline-block;
  margin-left: 5px;
  font-size: 0.65rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .panel-disc .current-price .tax-u {
    font-size: 2.3vw;
  }
}
#main #list .container .item-list.row > ul > li a .pre-bid {
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  -ms-grid-row: 2;
  -ms-grid-row-span: 1;
  grid-row: 2/3;
  gap: 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .pre-bid {
    gap: 1vw;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .pre-bid .end-v {
    background: url(../img/common/icn_clock_list.png) no-repeat;
    background-position: 0 calc(50% + 1px);
    background-size: 3.5vw auto;
  }
}
#main #list .container .item-list.row > ul > li a .pre-bid .end-v, #main #list .container .item-list.row > ul > li a .pre-bid .bid-v {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 1rem;
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .pre-bid .end-v, #main #list .container .item-list.row > ul > li a .pre-bid .bid-v {
    margin: 0;
    padding: 0 0 0 6vw;
  }
}
#main #list .container .item-list.row > ul > li a .pre-bid .end-v p, #main #list .container .item-list.row > ul > li a .pre-bid .bid-v p {
  width: auto;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .pre-bid .end-v p, #main #list .container .item-list.row > ul > li a .pre-bid .bid-v p {
    font-size: 3.5vw;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .pre-bid .end-v p span, #main #list .container .item-list.row > ul > li a .pre-bid .bid-v p span {
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row > ul > li a .pre-bid .end-v p span.time, #main #list .container .item-list.row > ul > li a .pre-bid .bid-v p span.time {
  display: block;
}
#main #list .container .item-list.row > ul > li a .product-wrap {
  position: relative;
  padding: 0 0 44px;
  width: calc(100% - 150px);
  min-height: 150px;
  border-left: 1px solid #f8f8f8;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap {
    width: calc(100% - 100px);
    padding: 0;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .item-name {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0.5rem 110px 0.5rem 1rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.3;
  background-color: #333;
  border: none;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .item-name {
    height: auto !important;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.8rem;
    line-height: 1.1rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .item-name .tag_status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  margin: 0;
  padding: 0;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .item-name .tag_status {
    position: static;
    display: block;
    width: 100%;
    padding: 0.5rem 0 0.3rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .item-name .tag_status > p {
  display: inline-block;
  margin: 0 2px 2px 0;
  padding: 4px 12px 5px;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .item-name .tag_status > p {
    padding: 5px 16px 6px;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .item-name .tag_status .status_recommend {
  color: #fff;
  background-color: #ff0000;
}
#main #list .container .item-list.row > ul > li a .product-wrap .current-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: #fff;
}
#main #list .container .item-list.row > ul > li a .product-wrap .current-price .price-v {
  display: inline-block;
  margin: 0 0.5rem;
  color: #E50A09;
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1.2;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .current-price .price-v {
    font-size: 1.4rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 100%;
  height: 44px;
  margin: 0 auto;
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl {
    position: static;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
    height: auto;
    border-bottom: 1px solid #fff;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dt {
  width: 180px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dt {
    width: 100%;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dt .bid-l {
  font-weight: 400;
  display: inline-block;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dt .bid-l {
    width: 4rem;
    font-size: 0.8rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dt .bid-v {
  margin: 0 0 0 1rem;
  font-weight: 600;
  display: inline-block;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dt .bid-v {
    margin: 0 0 0 0.5rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l {
  width: 140px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l {
    padding: 0;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l span.label {
    width: 4rem;
    font-size: 0.8rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l span.value {
  display: inline-block;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l span.value {
    font-size: 0.9rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-l span.limit {
  color: #ff0000;
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-v {
  width: calc(100% - 140px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-v {
    width: 100%;
    padding: 0;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-v span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-v span.label {
    width: 4rem;
    font-size: 0.8rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-v span.value {
  display: inline-block;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap dl dd .end-v span.value {
    font-size: 0.9rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid {
  -ms-grid-row: 1;
  -ms-grid-row-span: 3;
  grid-row: 1/4;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  position: relative;
  width: 360px;
  padding: 1rem 1rem calc(46px + 2rem);
  background-color: #f0f0f0;
}
@media screen and (max-width: 1080px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid {
    width: 240px;
    max-width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid {
    -ms-grid-row: 3;
    -ms-grid-row-span: 1;
    grid-row: 3/4;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    width: 100%;
    max-width: 100%;
    border-bottom: 1px solid #fff;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .ttl {
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  font-weight: 700;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .price {
  font-size: 1.4rem;
  font-weight: 700;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input {
  width: 7rem;
  margin: 0 0 0 1rem;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: right;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input::-webkit-input-placeholder {
  color: #ddd;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input::-moz-placeholder {
  color: #ddd;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input:-ms-input-placeholder {
  color: #ddd;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input::-ms-input-placeholder {
  color: #ddd;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input::placeholder {
  color: #ddd;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input.price-bid-comp {
  background-color: #e5e5e5;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input.price-bid-comp::-webkit-input-placeholder {
  color: #000;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input.price-bid-comp::-moz-placeholder {
  color: #000;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input.price-bid-comp:-ms-input-placeholder {
  color: #000;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input.price-bid-comp::-ms-input-placeholder {
  color: #000;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid input.price-bid-comp::placeholder {
  color: #000;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 1rem 0 1rem;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid ul > li > button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 5px 5px 0;
  padding: 0 7px 0 0;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #CDCBCA;
  border-radius: 30px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid ul > li > button {
    font-size: 1rem;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid ul > li > button span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0 7px;
  color: #fff;
  line-height: 1;
  background-color: #e98181;
  border-radius: 20px;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid ul > li > button span::after {
  content: "+";
  position: absolute;
  top: 0.7px;
  left: 5.5px;
  width: 14px;
  height: 14px;
  color: #fff;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid.invoice {
  margin: 3rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid.invoice {
    margin: 1rem 0 0;
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button {
  width: calc(100% - 50px - 1rem);
  height: 55px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #e98181;
  border-radius: 4px;
  line-height: 1.2;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button.invoice {
  width: 100%;
  padding: 1px 25% 3px 10px;
  text-align: right;
  background-image: url(../img/common/icn_download_list.svg);
  background-repeat: no-repeat;
  background-position: right 10% top 50%;
}
@media screen and (max-width: 1080px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button.invoice {
    text-align: center;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button.invoice {
    padding: 1px 10px 3px 10px;
    text-align: center;
    background-position: right calc(50% - 7.2rem) top 50%;
  }
}
@media screen and (max-width: 400px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button.invoice {
    padding: 1px 46px 3px 10px;
    background-position: right 6% top 50%;
  }
}
@media screen and (max-width: 1080px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button {
    width: auto;
    min-width: calc(100% - 55px - 1rem);
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid button {
    width: calc(100% - 55px - 0.5rem);
  }
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid .update {
  position: relative;
  width: 55px;
  height: 55px;
  margin: 0 0 0 1rem;
  padding: 1.5rem 0 0;
  color: #e98181;
  text-align: center;
  background-color: #fff;
  border: 1px solid #CDCBCA;
  border-radius: 30px;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid .update span {
  font-size: 0.8rem;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .button-bid .update::after {
  content: "";
  display: inline-block;
  background: url("../img/common/icn_update_list.svg") center 8px no-repeat;
  background-size: 20px auto;
  width: 30px;
  height: 30px;
  position: absolute;
  top: 0;
  left: calc(50% - 15px);
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .other-info-detail {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  width: calc(100% - 2rem);
  z-index: 10;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .other-info-detail button {
  position: relative;
  width: 100%;
  height: 46px;
  margin: 0;
  padding: 0.5rem 1rem;
  color: #e98181;
  font-size: 1rem;
  font-weight: 500;
  background-color: #fff;
  border: 2px solid #e98181;
  border-radius: 30px;
}
#main #list .container .item-list.row > ul > li a .product-wrap .place-bid .other-info-detail button::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #e98181;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#main #list .container .item-list.row > ul > li .btn-foreground-wrap {
  bottom: 1rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row > ul > li .btn-foreground-wrap {
    bottom: 4vw;
  }
}
#main #list .container .item-list.row > ul > li.soldout a figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/layer_soldout.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.panel {
  margin: 0 0 2rem;
  padding: 1rem 0 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.panel {
    padding: 4vw 0 0;
  }
}
#main #list .container .item-list.panel ul li a .panel-disc .item-name {
  color: #427fae;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.panel ul li a .panel-disc .item-name .name-title {
    display: inline-block;
    font-size: 2.8vw;
    line-height: 1.4;
    margin: 0 2vw 1vw 0;
  }
}
#main #list .container .item-list.panel ul li a .panel-disc .item-name .tab-item {
  padding: 1px 8px;
  color: #333;
  border: 1px solid #333;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.panel ul li a .panel-disc .item-name .tab-item {
    font-size: 2vw;
  }
}
#main #list .container .item-list.row-bid {
  margin: 0;
  text-align: center;
}
#main #list .container .item-list.row-bid.no-item {
  padding: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #fff;
}
#main #list .container .item-list.row-bid.no-item p.no-item-msg {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  color: #000;
}
#main #list .container .item-list.row-bid .label-bid-item {
  padding: 0.5rem 0;
  border-top: 1px solid #ccc;
}
#main #list .container .item-list.row-bid .label-bid-item p {
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid .label-bid-item p {
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row-bid ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 0;
  width: 100%;
}
#main #list .container .item-list.row-bid ul > li {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 150px 1rem 1fr 1rem auto;
  grid-template-columns: 150px 1fr auto;
  gap: 1rem;
  padding: 1rem 0;
  width: 100%;
  margin: 0;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    margin: 0 0 1.5 rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li.payment {
  border-top: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li.payment {
    padding: 1rem 0 0.5rem;
  }
}
#main #list .container .item-list.row-bid ul > li.soldout {
  position: relative;
}
#main #list .container .item-list.row-bid ul > li.soldout figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/layer_soldout.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li.soldout .place-bid:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li.closed {
  position: relative;
}
#main #list .container .item-list.row-bid ul > li.closed figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/layer_closed.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li.closed .place-bid:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li.preauc {
  position: relative;
}
#main #list .container .item-list.row-bid ul > li.preauc figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/layer_preauc.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li.preauc .place-bid:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li.extended {
  position: relative;
}
#main #list .container .item-list.row-bid ul > li.extended figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-image: url(../img/common/layer_extended.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #list .container .item-list.row-bid ul > li figure {
  position: relative;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 150px;
  height: 150px;
  border: 1px solid #e9eaeb;
}
@media screen and (max-width: 1080px) {
  #main #list .container .item-list.row-bid ul > li figure {
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li figure {
    width: calc(92vw - 2px);
    height: auto;
    margin: 0 0 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li figure img {
  display: block;
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
#main #list .container .item-list.row-bid ul > li figure .tab-f {
  position: absolute;
  top: 3px;
  left: 3px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li figure .tab-f {
    top: 2vw;
    left: 2vw;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    width: auto;
    height: auto;
  }
}
#main #list .container .item-list.row-bid ul > li figure .tab-f span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 20px;
  padding: 0 14px;
  font-size: 0.7rem;
  font-weight: 600;
  color: #fff;
  line-height: 1;
  background-color: #333;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li figure .tab-f span {
    height: 6vw;
    padding: 0 4vw;
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row-bid ul > li figure .tab-f span.delivery, #main #list .container .item-list.row-bid ul > li figure .tab-f span.title-a {
  color: #fff;
  background-color: #333;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc {
  position: relative;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc {
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    -ms-grid-row: 2;
    -ms-grid-row-span: 1;
    grid-row: 2/3;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name {
  position: relative;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
  width: 100%;
  margin: 0;
  padding: 0 0 0.5rem;
  color: #427fae;
  font-weight: 500;
  font-size: 0.9rem;
  border-bottom: 1px solid #e9eaeb;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .item-name {
    margin: 0;
    padding: 0 0 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name a {
  display: block;
  padding: 0;
  color: #427fae;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.5;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .item-name a {
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name a:hover {
  opacity: 1;
  background-color: transparent;
  text-decoration: underline;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name a .name-title {
  font-weight: 500;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name a .tab-item {
  display: inline-block;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0 10px;
  padding: 0 0.7rem;
  font-size: 0.65rem;
  font-weight: 600;
  line-height: 1rem;
  border: 1px solid;
  border-radius: 2px;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .item-name a .tab-item {
    margin: 0 2vw;
    padding: 0 4vw;
    font-size: 2.8vw;
    line-height: 3.8vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name.payment {
  border-bottom: none;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name.payment .name-title {
  color: #333;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .item-name.payment .name-title {
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .item-name.payment .tab-item {
  display: inline-block;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0 10px;
  padding: 0 0.7rem;
  color: #333;
  font-size: 0.65rem;
  font-weight: 600;
  line-height: 1rem;
  border: 1px solid #333;
  border-radius: 2px;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .item-name.payment .tab-item {
    margin: 0 2vw;
    padding: 0 4vw;
    font-size: 2.8vw;
    line-height: 3.8vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  gap: 0.5rem;
  padding: 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap {
    gap: 2vw 0;
    padding: 2vw 0 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
@media screen and (max-width: 1080px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  gap: 0;
  margin: 0;
  padding: 0;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  gap: 0;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price {
    width: 100%;
    max-width: 100%;
    min-height: 8vw;
    margin: 0;
    font-size: 3.6vw;
    line-height: 5.8vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-c {
  display: inline-block;
  margin-right: 5px;
  color: #231914;
  font-size: 0.7rem;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-c {
    font-size: 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-v {
  color: #E50A09;
  font-size: 1.2rem;
  font-weight: 700;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
  text-align: right;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-v {
    font-size: 5.8vw;
    text-align: right;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-v.bl {
  color: #333;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-v.sm {
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-v.sm {
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-u {
  color: #E50A09;
  font-size: 0.7rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-u {
    font-size: 2.6vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-u.bl {
  color: #333;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-u.sm {
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-u.sm {
    font-size: 2.6vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .price-u.thin {
  font-weight: 400;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .tax-u {
  display: inline-block;
  margin-left: 5px;
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .price-box .price .tax-u {
    font-size: 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .tab-wrap-status {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 3px;
  width: auto;
  margin: 0;
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .tab-wrap-status {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    width: auto;
    padding: 0;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .tab-wrap-status li {
  width: 100%;
  margin: 0;
  padding: 2px 7px;
  border: 1px solid #427fae;
  font-size: 0.8rem;
  font-weight: 600;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .tab-wrap-status li {
    width: auto;
    padding: 1vw 4vw;
    font-size: 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .tab-wrap-status li.top {
  padding: 3px 10px;
  color: #fff;
  background-color: #e98181;
  border: 1px solid #e98181;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .desc-p-top .tab-wrap-status li.min-bid {
  padding: 3px 8px;
  color: #e98181;
  border: 1px solid #e98181;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 3px;
  width: 100%;
  margin: 0;
  padding: 0;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .tab-wrap li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: auto;
  margin: 0;
  padding: 1px 8px;
  font-size: 0.6rem;
  font-weight: 500;
  white-space: nowrap;
  border: 1px solid #333;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .tab-wrap li {
    font-size: 2vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .tab-wrap li.tab-main {
  color: #fff;
  background-color: #333;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .tab-wrap li.tab-sub {
  border: 1px solid #333;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .tab-wrap li.tab-standard {
  color: #333;
  border: 1px solid #333;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: calc(100% - 100px);
  margin: auto auto 0 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid {
    width: 100%;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    padding: 0;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 16px;
  margin: 0;
  padding: 3px 0 3px 1.4rem;
  border: none;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 1vw 0 1vw 6vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p {
  width: 100%;
  font-size: 0.9rem;
  font-weight: 700;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p {
    font-size: 3.5vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.date, #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.time {
  margin-right: 0.5rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.date, #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.time {
    margin-right: 2vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.red {
  color: #E50A09;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.end {
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li p span.end {
    font-size: 3.5vw;
    font-weight: 500;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.bid-v {
  width: 80px;
  background: url("../img/common/icn_hammer_list.png") no-repeat;
  background-size: 14px auto;
  background-position: left 50%;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.bid-v {
    width: 16vw;
    background-size: 3.5vw auto;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.view {
  width: 80px;
  background: url("../img/common/icn_eye_list.svg") no-repeat;
  background-size: 16px auto;
  background-position: left 50%;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.view {
    width: 16vw;
    background-size: 3.7vw auto;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.end-v {
  width: auto;
  padding: 0 0 1px 1.4rem;
  line-height: 1;
  background: url("../img/common/icn_clock_list.png") no-repeat;
  background-size: 15px auto;
  background-position: left calc(50% + 0px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.end-v {
    width: 100%;
    padding: 1vw 0 0 6vw;
    background-size: 3.5vw auto;
    background-position: left calc(50% + 0.7vw);
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.favo {
  width: 70px;
  background: url("../img/common/icn_favorite.svg") no-repeat;
  background-size: 16px auto;
  background-position: left 50%;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid li.favo {
    width: 16vw;
    background-size: 3.5vw auto;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid.sealed {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid.sealed .end-label {
  width: auto;
  margin-right: 5px;
  padding: 0;
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid.sealed .end-label {
    margin-right: 8px;
    font-size: 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid.sealed .end-v {
  width: auto;
  padding: 0 0 1px 1.2rem;
  background-position: left calc(50% + 1px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .pre-bid.sealed .end-v {
    padding: 1vw 0 0 4vw;
    background-position: left calc(50% + 3px);
  }
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .item-p-desc .summary-wrap .btn-foreground-wrap {
    position: static;
    width: 100%;
    margin: 0;
    padding: 1vw 0 0;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid {
  position: relative;
  width: 360px;
  max-width: 40vw;
  padding: 1.5rem 1rem;
  background-color: #f5f5f5;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid {
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    -ms-grid-row: 3;
    -ms-grid-row-span: 1;
    grid-row: 3/4;
    width: 100%;
    max-width: 100%;
    padding: 6vw 4vw 6vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  width: 100%;
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .price {
    margin: 0 0 4vw;
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .price .winning-bid-price {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  width: calc(100% - 7rem);
  max-width: calc(100% - 7rem);
  margin: 0 0.5rem 0 0;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 700;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
  text-align: right;
  background-color: #e8e8e8;
  border: none;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .price .winning-bid-price {
    width: 40vw;
    padding: 3vw;
    font-size: 5.2vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .invoice {
  width: 100%;
  margin: 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .invoice {
    margin: 0 0 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .invoice button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 48px;
  color: #fff;
  background-color: #333;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .invoice button {
    height: 14vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .invoice button span {
  position: relative;
  display: inline-block;
  padding: 0 26px 0 0;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .invoice button span {
    padding: 0 6vw 0 0;
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .invoice button span::after {
  position: absolute;
  top: 3px;
  right: 0;
  content: "";
  display: inline-block;
  background: url(../img/common/icn_download_w.svg) center no-repeat;
  background-size: 17px auto;
  width: 18px;
  height: 18px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .invoice button span::after {
    top: 1vw;
    background-size: 4vw auto;
    width: 4vw;
    height: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .payment {
  width: 100%;
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .payment button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 48px;
  color: #fff;
  background-color: #fff;
  border: 1px solid #333;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .payment button {
    height: 14vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .payment button:hover {
  background-color: #f8f8f8;
}
#main #list .container .item-list.row-bid ul > li .place-bid.history .payment button span {
  position: relative;
  display: inline-block;
  padding: 0;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history .payment button span {
    padding: 0;
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history.payment {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  width: auto;
  min-width: 360px;
  padding: 0;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history.payment {
    padding: 0;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin: 0;
  font-size: 0.8rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price {
    font-size: 3.2vw;
    font-weight: 500;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price.pay {
  color: #E50A09;
}
#main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price.pay .winning-bid-price {
  font-size: 1.6rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price.pay .winning-bid-price {
    font-size: 5.5vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price .ttl {
  margin: 0 0.5rem 0 0;
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price .ttl {
    font-size: 3.2vw;
    font-weight: 500;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price .winning-bid-price {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  width: auto;
  max-width: 100%;
  margin: 0;
  font-size: 0.9rem;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid.history.payment .price .winning-bid-price {
    padding: 1vw;
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  width: 100%;
  font-size: 1rem;
  font-weight: 600;
  text-align: left;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price .ttl {
  margin: 0 auto 0 0;
  font-size: 1rem;
  font-weight: 700;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .price .ttl {
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  width: calc(100% - 7rem);
  max-width: calc(100% - 7rem);
  margin: 0 0.5rem 0 0;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 700;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
  text-align: right;
  border: none;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .price input {
    width: 40vw;
    font-size: 5.8vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input::-webkit-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input::-moz-placeholder {
  color: #ddd;
  font-size: 1.4rem;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input:-ms-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input::-ms-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input::placeholder {
  color: #ddd;
  font-size: 1.4rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .price input::-webkit-input-placeholder {
    font-size: 5.8vw;
  }
  #main #list .container .item-list.row-bid ul > li .place-bid .price input::-moz-placeholder {
    font-size: 5.8vw;
  }
  #main #list .container .item-list.row-bid ul > li .place-bid .price input:-ms-input-placeholder {
    font-size: 5.8vw;
  }
  #main #list .container .item-list.row-bid ul > li .place-bid .price input::-ms-input-placeholder {
    font-size: 5.8vw;
  }
  #main #list .container .item-list.row-bid ul > li .place-bid .price input::placeholder {
    font-size: 5.8vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input.price-bid-comp {
  background-color: #e5e5e5;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input.price-bid-comp::-webkit-input-placeholder {
  color: #000;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input.price-bid-comp::-moz-placeholder {
  color: #000;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input.price-bid-comp:-ms-input-placeholder {
  color: #000;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input.price-bid-comp::-ms-input-placeholder {
  color: #000;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price input.price-bid-comp::placeholder {
  color: #000;
}
#main #list .container .item-list.row-bid ul > li .place-bid .price .unit {
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .price .unit {
    font-size: 4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px 5px;
  width: 100%;
  margin: 1rem 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit {
    margin: 4vw 0;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit li {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin: 0;
  padding: 0;
  border: none;
  -ms-grid-columns: 1fr;
  grid-template-columns: 1fr;
}
#main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit li .bid-unit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0.2rem 0.4rem 0.2rem 0.2rem;
  font-size: 0.8rem;
  font-weight: 600;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
  background-color: #fff;
  border-radius: 4px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit li .bid-unit {
    max-width: 100%;
    padding: 1vw;
    font-size: 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit li .bid-unit span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 0 5px 0 0;
  padding: 0 7px;
  color: #fff;
  line-height: 1;
  background-color: #427fae;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit li .bid-unit span {
    width: 5vw;
    height: 5vw;
    margin: 0 1.5vw 0 0;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid ul.bidding-unit li .bid-unit span::after {
  content: "+";
  position: absolute;
  top: 0;
  left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 1rem;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 55px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #427fae;
  border-radius: 50px;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid .btn {
    height: 14vw;
    font-size: 4.6vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .btn:hover {
  opacity: 0.8;
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .btn .pct {
  width: 16px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid .btn .pct {
    width: 4.4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .btn .bid {
  position: relative;
  width: auto;
  display: inline-block;
  padding-left: 14px;
  font-weight: 600;
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update {
  position: relative;
  width: 55px;
  height: 55px;
  margin: 0 0 0 0.8rem;
  padding: 1.5rem 0 0;
  color: #427fae;
  text-align: center;
  background-color: #fff;
  border: 1px solid #427fae;
  border-radius: 30px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update {
    width: 14vw;
    height: 14vw;
    margin: 0 0 0 3vw;
    padding: 7vw 0 0;
    border-radius: 30vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update:hover {
  opacity: 0.8;
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update span {
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update span {
    font-size: 3vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update::after {
  content: "";
  display: inline-block;
  background: url("../img/common/icn_refresh_blue.svg") center no-repeat;
  background-size: 18px auto;
  width: 22px;
  height: 22px;
  position: absolute;
  top: 6px;
  left: calc(50% - 11px);
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid .update::after {
    background: url("../img/common/icn_refresh_blue.svg") center no-repeat;
    background-size: 4.8vw auto;
    width: 5.5vw;
    height: 5.5vw;
    top: 1.8vw;
    left: calc(50% - 2.75vw);
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 0.5rem 0 0;
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel .btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 55px;
  color: #e98181;
  font-size: 1rem;
  font-weight: 500;
  background-color: #fff;
  border: 1px solid #e98181;
  border-radius: 50px;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel .btn {
    height: 14vw;
    font-size: 4.6vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel .btn:hover {
  color: #fff;
  background-color: #e98181;
  border-color: #e98181;
  opacity: 1;
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel .btn .pct {
  width: 16px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel .btn .pct {
    width: 4.4vw;
  }
}
#main #list .container .item-list.row-bid ul > li .place-bid .button-bid-cancel .btn .bid {
  position: relative;
  width: auto;
  display: inline-block;
  padding-left: 14px;
  font-weight: 600;
}

/*** 封印入札式 ***/
#list-sealed {
  background-color: #fff;
}

#main #list.sealed .item-list.panel a {
  padding: 0 0 40px;
}
@media screen and (max-width: 767px) {
  #main #list.sealed .item-list.panel a {
    padding: 0 0 12vw;
  }
}
#main #list.sealed .item-list.row a .pre-bid.sealed {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
@media screen and (max-width: 767px) {
  #main #list.sealed .item-list.row a .pre-bid.sealed {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0;
  }
}
#main #list.sealed .item-list.row a .pre-bid.sealed .end-label {
  width: auto;
  margin-right: 5px;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #list.sealed .item-list.row a .pre-bid.sealed .end-label {
    font-size: 3vw;
  }
}
#main #list.sealed .item-list.row a .pre-bid.sealed .end-v {
  width: auto;
}
#main #list.sealed .item-list.row .btn-foreground-wrap.sealed {
  bottom: 1rem;
}
@media screen and (max-width: 767px) {
  #main #list.sealed .item-list.row .btn-foreground-wrap.sealed {
    bottom: 4vw;
  }
}
#main #list.sealed .item-list.row-bid .summary-wrap .pre-bid.sealed {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media screen and (max-width: 767px) {
  #main #list.sealed .item-list.row-bid .summary-wrap .pre-bid.sealed {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0;
  }
}
#main #list.sealed .item-list.row-bid .summary-wrap .pre-bid.sealed .end-label {
  width: auto;
  margin-right: 5px;
  padding: 0;
  font-size: 0.7rem;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #main #list.sealed .item-list.row-bid .summary-wrap .pre-bid.sealed .end-label {
    font-size: 3vw;
  }
}
#main #list.sealed .item-list.row-bid .summary-wrap .pre-bid.sealed .end-v {
  width: auto;
  font-size: 0.8rem;
}

.wrap-btn.pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  margin: 40px 0;
  font-family: "Helvetica Neue", sans-serif;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination {
    margin: 7vw 0;
  }
}
.wrap-btn.pagination p {
  margin: 0 0 1rem;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination p {
    margin: 0 0 4vw;
    font-size: 3.2vw;
  }
}
.wrap-btn.pagination ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  gap: 8px;
  padding: 0;
  margin: 0;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul {
    gap: 2vw;
  }
}
.wrap-btn.pagination ul li {
  position: relative;
  width: auto;
  min-width: 40px;
  height: 40px;
  min-height: 40px;
  border: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul li {
    height: 10vw;
  }
}
.wrap-btn.pagination ul li.prev {
  width: 40px;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul li.prev {
    width: 10vw;
  }
}
.wrap-btn.pagination ul li.prev:after {
  content: "";
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 1px #333;
  border-right: solid 1px #333;
  position: absolute;
  top: calc(50% + 1px);
  left: calc(50% - 3px);
  margin-top: -4px;
  -webkit-transform: rotate(-135deg);
  transform: rotate(-135deg);
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul li.prev:after {
    width: 1.5vw;
    height: 1.5vw;
    top: calc(50% - 0vw);
    left: calc(50% - 0.7vw);
  }
}
.wrap-btn.pagination ul li.next {
  width: 40px;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul li.next {
    width: 10vw;
  }
}
.wrap-btn.pagination ul li.next:after {
  content: "";
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 1px #333;
  border-right: solid 1px #333;
  position: absolute;
  top: calc(50% + 1px);
  left: calc(50% - 6px);
  margin-top: -4px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul li.next:after {
    width: 1.5vw;
    height: 1.5vw;
    top: 50%;
    left: calc(50% - 1.5vw);
  }
}
.wrap-btn.pagination ul li:has(> a.active) {
  border-color: #000;
}
.wrap-btn.pagination ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
  padding: 6px 10px;
  color: #333;
  font-weight: 400;
  text-decoration: none;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  .wrap-btn.pagination ul li a {
    min-width: calc(10vw - 2px);
    min-height: calc(10vw - 2px);
    font-size: 3.5vw;
  }
}
.wrap-btn.pagination ul li a:hover {
  background-color: #f0f0f0;
}
.wrap-btn.pagination ul li a.active {
  background-color: #333;
  color: #fff;
  border-radius: 0;
}

#main #list.list-item.payment {
  width: 1000px;
  max-width: 100%;
  padding: 0;
}
/*# sourceMappingURL=list.css.map */