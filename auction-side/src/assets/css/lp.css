/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *LP
 * *********************************************************************** */
#main .other .container .lp-ttl {
  margin: 2.5rem 0 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .other .container .lp-ttl {
    margin: 7vw 0 2vw;
    font-size: 4vw;
  }
}
#main .other .container .lp-ttl.first {
  margin: 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main .other .container .lp-ttl.first {
    margin: 0 0 2vw;
  }
}
#main .other .container .lp-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
@media screen and (max-width: 767px) {
  #main .other .container .lp-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main .other .container .lp-content .text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
#main .other .container .lp-content .text p {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main .other .container .lp-content .text p {
    font-size: 3.2vw;
  }
}
#main .other .container .lp-content .text .list_nmb-o3 {
  list-style: none;
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main .other .container .lp-content .text .list_nmb-o3 {
    padding: 0 0 0 3.8vw;
  }
}
#main .other .container .lp-content .text .list_nmb-o3 li {
  margin: 0;
  font-size: 1rem;
  text-indent: -1rem;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #main .other .container .lp-content .text .list_nmb-o3 li {
    font-size: 3.2vw;
  }
}
#main .other .container .lp-content .illust {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 100%;
  max-width: 360px;
  padding-left: 2rem;
}
#main .other .container .lp-content .illust figure img {
  height: auto;
}
#main .other .container .lp-content .illust figure img.pct-auc {
  max-width: 100%;
}
/*# sourceMappingURL=lp.css.map */