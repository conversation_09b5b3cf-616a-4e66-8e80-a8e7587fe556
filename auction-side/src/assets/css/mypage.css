@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *マイページ
 * *********************************************************************** */
/* Nav
 * *========================================== */
#main #mypage-head {
  width: 100%;
  padding: 0;
  border-bottom: 1px solid #ccc;
}
#main #mypage-head .container {
  width: 1280px;
  max-width: 100%;
  padding: 0;
}
#main #mypage-head .container .nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 0;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}
#main #mypage-head .container .nav-wrap .nav-content {
  width: 20%;
  max-width: 100%;
  height: 80px;
  margin: 0;
  padding: 0;
  border-radius: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content {
    width: 25%;
    height: 15vw;
    min-height: 12vw;
    margin: 0;
  }
}
#main #mypage-head .container .nav-wrap .nav-content:hover {
  opacity: 1;
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content:hover span.upload {
    margin-left: 1px;
    background-size: 11px auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.card {
  background-image: url(../img/common/icn_mypage_nav_card.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover .label {
  color: #333;
}
#main #mypage-head .container .nav-wrap .nav-content a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 3.2vw 0 2vw;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span {
  display: inline-block;
  content: "";
  background-position: center;
  background-size: 18px auto;
  background-repeat: no-repeat;
  width: 18px;
  height: 18px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content a span {
    background-size: 16px auto;
    width: 16px;
    height: 16px;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a span {
    top: 0;
    left: calc(50% - 2.75vw);
    width: 5.5vw;
    height: 5.5vw;
    background-size: 5vw auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.account {
  background-image: url(../img/common/icn_mypage_nav_account_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload_pgr.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content a span.upload {
    margin-left: 1px;
    background-size: 11px auto;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a span.upload {
    position: absolute;
    top: 3.6vw;
    left: 12vw;
    width: 3vw;
    height: 3.5vw;
    background-size: 2.8vw auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span.card {
  background-image: url(../img/common/icn_mypage_nav_card_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a .label {
  display: inline-block;
  padding: 0 0 0 5px;
  color: #c9c9c9;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content a .label {
    font-size: 0.8rem;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a .label {
    margin: auto 0 0;
    padding: 0;
    font-size: 2.2vw;
  }
}
#main #mypage-head .container .nav-wrap .nav-content.active {
  background-color: transparent;
  border-bottom: 3px solid #333;
}
#main #mypage-head .container .nav-wrap .nav-content.active a {
  position: relative;
  padding: 3px 0 0;
  cursor: default;
  pointer-events: none;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content.active a {
    padding: 3.2vw 0 calc(2vw - 3px);
  }
}
#main #mypage-head .container .nav-wrap .nav-content.active a:hover {
  opacity: 1;
}
#main #mypage-head .container .nav-wrap .nav-content.active a .favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .bidding {
  background-image: url(../img/common/icn_mypage_nav_bid.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .upload {
  background-image: url(../img/common/icn_mypage_nav_upload.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .card {
  background-image: url(../img/common/icn_mypage_nav_card.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .label {
  color: #333;
  font-weight: 600;
}
#main .mypage-payment {
  width: 1000px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1rem 100px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment {
    padding: 0 0 10vw;
  }
}
#main .mypage-payment .payment-method {
  width: 100%;
  margin: 1rem auto 0;
  padding: 2rem 3rem;
  background-color: #f5f5f5;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method {
    padding: 10vw 4vw;
  }
}
#main .mypage-payment .payment-method .ttl {
  margin: 1rem 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .ttl {
    font-size: 4.5vw;
  }
}
#main .mypage-payment .payment-method .payment-option {
  width: 100%;
  margin: 1rem 0;
  padding: 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #333;
}
#main .mypage-payment .payment-method .payment-option:hover {
  opacity: 0.8;
}
#main .mypage-payment .payment-method .payment-option label {
  display: block;
  width: 100%;
  height: 100%;
  padding: 1.3rem 2rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .payment-option label {
    font-size: 0.9rem;
    padding: 1rem 1.2rem;
  }
}
#main .mypage-payment .payment-method .payment-option label input {
  display: inline-block;
  margin: 0 1rem 0 0;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .payment-option label input {
    margin: 0 0.5rem 0 0;
  }
}
#main .mypage-payment .payment-method .invoice-wrap {
  width: 800px;
  max-width: calc(100% - 2rem);
  margin: 2rem auto 5rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap {
    width: 100%;
    max-width: 100%;
    margin: 1rem 0 3rem;
  }
}
#main .mypage-payment .payment-method .invoice-wrap h4 {
  margin: 1rem 0;
  padding: 0 0 0.2rem;
  font-weight: 700;
  font-size: 1.2rem;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap h4 {
    padding: 0 0 0.2rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #ccc;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient {
  width: 100%;
  margin: 0 0 3rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient {
    margin: 0 0 1rem;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr {
  padding: 0.3rem 0;
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr th {
  font-size: 1rem;
  font-weight: 700;
  vertical-align: middle;
  width: 180px;
  position: relative;
  padding: 0 2rem;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr th {
    display: block;
    width: 100%;
    padding: 0;
    font-size: 0.9rem;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr th em.req {
  position: absolute;
  top: 50%;
  right: 10px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: inline-block;
  background-color: #E80000;
  width: 35px;
  height: 20px;
  color: #fff;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  line-height: 19px;
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr td {
  width: calc(100% - 1rem);
  padding: 15px 0;
  position: relative;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr td {
    display: block;
    width: 100%;
    padding: 10px 0 20px;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr td input.zip-search {
  margin: 5px 0 5px 15px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr td input.zip-search {
    width: 80px;
    padding-left: 5px;
    padding-right: 5px;
    margin: 0 0 0 15px;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-S {
    width: calc(100% - 80px - 1rem);
  }
}
#main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-M {
  width: calc(100% - 1.5rem);
  max-width: 100%;
}
@media screen and (max-width: 1080px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-M {
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-M {
    width: 100%;
    max-width: 100%;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .transfer-details {
  padding: 1rem 3rem 1.5rem;
  background-color: #f7f7f7;
  border: 1px solid #d1d1d1;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .transfer-details {
    padding: 1rem 1rem 1.5rem;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .transfer-details h5 {
  margin: 0.5rem 0;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .transfer-details h5 {
    margin: 0 0 0.5rem;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .transfer-details dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: 100%;
  font-size: 0.9rem;
}
#main .mypage-payment .payment-method .invoice-wrap .transfer-details dl dt {
  width: 110px;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .transfer-details dl dt {
    width: 80px;
  }
}
#main .mypage-payment .payment-method .invoice-wrap .transfer-details dl dd {
  width: calc(100% - 110px);
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .invoice-wrap .transfer-details dl dd {
    width: calc(100% - 80px);
  }
}
#main .mypage-payment .payment-method .note-no-card {
  width: 100%;
  margin: 60px 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .note-no-card {
    margin: 10vw 0;
    font-size: 3.5vw;
  }
}
#main .mypage-payment .payment-method .card-information-input {
  width: 740px;
  max-width: 100%;
  margin: 0 auto;
}
#main .mypage-payment .payment-method .card-information-input #entry-form {
  margin: 2rem 0;
  padding: 0;
  background-color: transparent;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing {
    gap: 1.5vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing:hover .radio-ui .ring {
  border-color: #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing:hover .select-card-type {
  color: #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing input[type=radio] {
  position: absolute;
  inline-size: 1px;
  block-size: 1px;
  margin: 0;
  opacity: 0;
  pointer-events: none;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing input[type=radio]:focus-visible + .radio-ui .ring {
  outline: 3px solid rgba(33, 150, 243, 0.35);
  outline-offset: 2px;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing input[type=radio]:checked + .radio-ui .dot {
  opacity: 1;
  -webkit-transform: scale(0.55);
          transform: scale(0.55);
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing input[type=radio]:checked + .radio-ui .ring {
  border-color: #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing input[type=radio]:disabled + .radio-ui {
  opacity: 0.6;
  cursor: not-allowed;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing input[type=radio]:disabled ~ .select-card-type {
  opacity: 0.6;
  cursor: not-allowed;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing .radio-ui {
  inline-size: 20px;
  block-size: 20px;
  height: 20px;
  display: -ms-inline-grid;
  display: inline-grid;
  place-items: center;
  position: relative;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing .radio-ui .ring {
  position: absolute;
  inline-size: 100%;
  block-size: 100%;
  border: 2px solid #ccc;
  border-radius: 50%;
  background: #fff;
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06) inset;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06) inset;
  -webkit-transition: border-color 0.2s ease, -webkit-box-shadow 0.2s ease;
  transition: border-color 0.2s ease, -webkit-box-shadow 0.2s ease;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, -webkit-box-shadow 0.2s ease;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing .radio-ui .dot {
  position: absolute;
  inline-size: 100%;
  block-size: 100%;
  border-radius: 50%;
  background: #427fae;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
  opacity: 0;
  -webkit-transition: opacity 0.15s ease, -webkit-transform 0.15s ease;
  transition: opacity 0.15s ease, -webkit-transform 0.15s ease;
  transition: opacity 0.15s ease, transform 0.15s ease;
  transition: opacity 0.15s ease, transform 0.15s ease, -webkit-transform 0.15s ease;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .ttl-card-existing .select-card-type {
  font-weight: 600;
  line-height: 1.4;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice {
  width: calc(100% - 48px);
  margin: 0 0 40px auto;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice {
    width: calc(100% - 14vw);
    margin: 0 4vw 7vw auto;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice th.ttl-l-choice {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100px;
  padding: 1rem 0 0;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice th.ttl-l-choice {
    display: none;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice th.ttl-l-choice span {
  font-size: 0.9rem;
  font-weight: 600;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice {
  width: calc(100% - 100px);
  padding: 0;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: 100%;
  min-height: 54px;
  margin: 0 0 0.3rem;
  padding: 0.8rem 1.5rem;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 6px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label {
    padding: 4vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label:hover {
  background-color: #ecf2f7;
  border: 1px solid #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label input {
  display: inline-block;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label:has(input[type=radio]:checked) {
  background-color: #ecf2f7;
  border: 1px solid #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label p .label {
  display: inline-block;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label p .label {
    font-size: 2.8vw;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label p span {
    font-size: 3.2vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label p.card-number {
  margin: 0 2rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label p.card-number {
    margin: 0 0 0 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice td.cont-choice label p.expiration-date {
    margin: 0 0 0 calc(21px + 4vw);
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice.disabled td.cont-choice label {
  background-color: #eee;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: default;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice.disabled td.cont-choice label:has(input[type=radio]:checked) {
  background-color: #eee;
  border: 1px solid #ccc;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice.disabled td.cont-choice label p span {
  color: #afafaf;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-choice.disabled td.cont-choice input:has(input[type=radio]:checked) {
  background-color: #eee;
  border: 1px solid #ccc;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice {
  max-width: 100%;
  padding: 0;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  min-height: 54px;
  margin: 0 0 0.3rem;
  padding: 0.8rem 1.5rem;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 6px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap {
    padding: 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap {
    width: calc(100% - 90px);
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap p .label {
  display: inline-block;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap p .label {
    font-size: 3vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap p.card-number {
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap p.expiration-date {
    margin: 0;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap p span {
    font-size: 3.2vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap .common-use {
  margin: 10px 0 0;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap .common-use span {
  padding: 3px 20px;
  color: #e98181;
  font-size: 0.8rem;
  font-weight: 600;
  background-color: #fef6f6;
  border: 1px solid #fef6f6;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .card-cont-wrap .common-use span {
    display: inline-block;
    margin: 0 auto 0 0;
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  gap: 10px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit {
    width: 90px;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn {
  display: block;
  width: 40px;
  padding: 22px 0 4px;
  background-color: #fff;
  background-repeat: no-repeat;
  border-radius: 4px;
  -webkit-transition: background-color 0.15s ease;
  transition: background-color 0.15s ease;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn:hover {
  background-color: #f5f5f5;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn.edit {
  background-image: url(../img/common/icn_pencil.svg);
  background-size: 20px 20px;
  background-position: center 5px;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn.edit:hover {
  background-image: url(../img/common/icn_pencil_blue.svg);
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn.edit:hover span {
  color: #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn.delete {
  background-image: url(../img/common/icn_delete.svg);
  background-size: 20px 20px;
  background-position: center 5px;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn.delete:hover {
  background-image: url(../img/common/icn_delete_blue.svg);
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn.delete:hover span {
  color: #427fae;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .tbl-card-setting .cont-choice .card-li-wrap .btn-edit .btn span {
  font-size: 10px;
  font-weight: 500;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap {
  display: none;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration {
  width: calc(100% - 48px);
  margin: 0 0 0 auto;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration {
    width: calc(100% - 10vw);
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr th {
  width: 200px;
  padding: 0 0 0.3rem;
  font-size: 0.9rem;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td {
  padding: 0 0 0.3rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td {
    width: calc(100% - 4vw);
    margin: 0 4vw 0 0;
    padding: 0 0 4vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .select-wrap {
  position: relative;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .select-wrap:after {
  position: absolute;
  content: "";
  right: 15px;
  top: 50%;
  width: 4px;
  height: 4px;
  border-right: 2px solid #bcbcbc;
  border-bottom: 2px solid #bcbcbc;
  -webkit-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .select-wrap select.iptW-S {
  width: 140px;
  height: 54px;
  padding: 11px 30px 11px 20px;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .txt-obj {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 54px;
  padding: 0 0.5rem;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0;
  color: #fff;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .head {
    font-size: 0.8rem;
    background-color: #52594f;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .head .use {
  width: 30%;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .head .use {
    width: 100%;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .head .num {
  width: 70%;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .head .num {
    display: none;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 5px 0 0;
  padding: 1rem 1rem;
  background-color: #fff;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card {
    padding: 0.8rem;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card:hover {
  background-color: #fcfbfb;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card input {
  display: inline-block;
  margin: 0 calc(30% - 40px) 0 20px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card input {
    margin: 0 25px 0 10px;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card input[type=radio]:checked + label {
  border: 1px solid #eaf1fa;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody tr td .card-select-panel .body .card p {
  width: 100%;
  text-align: right;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody .default-setting th, #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody .default-setting td {
  padding: 1rem 0;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody .default-setting th, #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody .default-setting td {
    padding: 0 0 0.3rem;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody .default-setting th {
    padding: 0 0 2vw;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .tbl-entry.card-registration tbody .default-setting td input {
  scale: 1.4;
}
#main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .note {
  padding: 1rem 0 1rem 3rem;
  font-size: 0.8rem;
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .card-registration-wrap .note {
    padding: 4vw 0 4vw calc(10vw + 1.1em);
    font-size: 3vw;
    text-indent: -1.1em;
  }
}
#main .mypage-payment .payment-method .card-information-input #entry-form .err-message.history-payment {
  padding-left: 3rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input #entry-form .err-message.history-payment {
    padding-left: 8.5vw;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-information-input .btn-form.card {
    width: calc(100% - 8vw);
    margin: 7vw auto;
  }
}
#main .mypage-payment .payment-method .card-selection {
  width: 800px;
  max-width: calc(100% - 2rem);
  margin: 3rem auto 5rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection {
    max-width: 100%;
    margin: 2rem auto 2rem;
  }
}
#main .mypage-payment .payment-method .card-selection .card-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 1rem;
  margin: 0 0 2rem;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-list {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin: 0 0 7vw;
  }
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel {
  width: calc(50% - 0.5rem);
  padding: 20px;
  background-image: -webkit-gradient(linear, left top, right top, from(#606060), to(#4e4e4e));
  background-image: linear-gradient(90deg, #606060, #4e4e4e);
  border-radius: 8px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-list .card-panel {
    width: 100%;
  }
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-brand {
  width: 100%;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-brand span {
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info {
  padding: 0 0 1rem;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .num {
  width: 100%;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .num span {
  margin-right: 10px;
  color: #fff;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .num span:last-child {
  margin-right: 0;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .main-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .main-info .name {
  line-height: 1.3;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .main-info .name span {
  margin-right: 10px;
  color: #fff;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .main-info .name span:last-child {
  margin-right: 0;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .main-info .date-ex {
  min-width: 3.5rem;
  line-height: 1.3;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .card-info .main-info .date-ex span {
  color: #fff;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-top: 1px solid #979797;
  padding: 0.8rem 0 0;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope {
  position: relative;
  padding: 8px 0 8px 28px;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope input[type=checkbox] {
  position: absolute;
  inset: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
  cursor: pointer;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope input[type=checkbox]:checked + span::before {
  background-color: #e98181;
  border-color: #989898;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope input[type=checkbox]:checked + span::after {
  content: "";
  position: absolute;
  left: 7px;
  top: calc(50% - 1px);
  -webkit-transform: translateY(-55%) rotate(45deg);
          transform: translateY(-55%) rotate(45deg);
  width: 4px;
  height: 11px;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope:hover span {
  opacity: 0.8;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope span {
  color: #fff;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .ope span::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 21px;
  height: 21px;
  background-color: #fff;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .btn-edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 5px;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .btn-edit .btn {
  width: 40px;
  height: 30px;
  padding: 22px 0 0;
  background-color: transparent;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .btn-edit .btn:hover {
  opacity: 0.8;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .btn-edit .btn.edit {
  background-image: url(../img/common/icn_pencil_w.svg);
  background-repeat: no-repeat;
  background-size: 20px 20px;
  background-position: center;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .btn-edit .btn.delete {
  background-image: url(../img/common/icn_delete_w.svg);
  background-repeat: no-repeat;
  background-size: 20px 20px;
  background-position: center;
}
#main .mypage-payment .payment-method .card-selection .card-list .card-panel .control .btn-edit .btn span {
  color: #fff;
  font-size: 10px;
}
#main .mypage-payment .payment-method .card-selection .btn-wrap {
  margin: 1rem 0;
  text-align: center;
}
#main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card {
  position: relative;
  padding: 1rem 1.5rem 1rem 3.5rem;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.1;
  background-color: #fff;
  border: 1px solid #333;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card {
    width: 100%;
    padding: 4vw 4vw;
    font-size: 3.2vw;
  }
}
#main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card:hover {
  -webkit-filter: brightness(0.98);
          filter: brightness(0.98);
}
#main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card:active {
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
#main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card:focus-visible {
  outline: 3px solid #93c5fd;
  outline-offset: 2px;
}
#main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card::before, #main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card::after {
  content: "";
  position: absolute;
  left: 24px;
  top: 50%;
  width: 16px;
  height: 2px;
  background: currentColor;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-radius: 1px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card::before, #main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card::after {
    left: 12vw;
  }
}
#main .mypage-payment .payment-method .card-selection .btn-wrap button.new-card::after {
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.5rem 1rem;
  color: #fff;
  background-color: #333;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .head {
    font-size: 0.8rem;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .head .use {
  width: 30%;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .head .use {
    width: 100%;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .head .num {
  width: 70%;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .head .num {
    display: none;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .head .num span {
  display: inline-block;
  width: 50%;
  font-weight: 500;
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 5px 0 0;
  padding: 0.8rem 1rem;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .body .card {
    padding: 0.8rem;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card:hover {
  background-color: #fcfbfb;
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card input {
  display: inline-block;
  margin: 0 calc(30% - 40px) 0 20px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .body .card input {
    margin: 0 25px 0 10px;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card input[type=radio]:checked + label {
  border: 1px solid #eaf1fa;
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .body .card dl {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card dl dt {
  width: 50%;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .body .card dl dt {
    width: 100%;
  }
}
#main .mypage-payment .payment-method .card-selection .card-select-panel .body .card dl dd {
  width: 50%;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .card-selection .card-select-panel .body .card dl dd {
    width: 100%;
  }
}
#main .mypage-payment .payment-method h5.ttl-method {
  font-size: 1.4rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method h5.ttl-method {
    font-size: 4.5vw;
  }
}
#main .mypage-payment .payment-method .method-cont-wrap {
  margin: 1rem 0 0;
}
#main .mypage-payment .payment-method .method-cont-wrap .confirm-card-info {
  padding: 1.5rem 60px;
  background-color: #eee;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .method-cont-wrap .confirm-card-info {
    padding: 6vw 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .method-cont-wrap .confirm-card-info p {
    font-size: 3.5vw;
  }
}
#main .mypage-payment .payment-method .method-cont-wrap .confirm-card-info p.text-complete {
  font-size: 1.4rem;
  font-weight: 600;
  text-align: center;
}
#main .mypage-payment .payment-method .method-cont-wrap .confirm-card-info p.v-cont {
  font-weight: 600;
}
#main .mypage-payment .payment-method .method-cont-wrap .confirm-card-info p span {
  display: inline-block;
  margin-right: 0.5rem;
  font-weight: 500;
}
#main .mypage-payment .payment-method .method-cont-wrap .text-complete {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .method-cont-wrap .text-complete {
    font-size: 4vw;
  }
}
#main .mypage-payment .payment-method .method-cont-wrap .note-complete {
  font-size: 0.9rem;
}
#main .mypage-payment .payment-method .method-cont-wrap .note {
  margin: 1rem 0;
}
#main .mypage-payment .payment-method .method-cont-wrap .note ol {
  padding-left: 0.8rem;
}
#main .mypage-payment .payment-method .method-cont-wrap .note ol li {
  text-indent: -0.8rem;
  font-size: 0.7rem;
  line-height: 1.4;
}
#main .mypage-payment .payment-method .method-cont-wrap .note.complete ol li {
  font-size: 0.9rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .method-cont-wrap .note.complete ol li {
    font-size: 3.2vw;
  }
}
#main .mypage-payment .payment-method .btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 40px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .btn-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 2vw;
  }
}
#main .mypage-payment .payment-method .btn-wrap.new-card {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
#main .mypage-payment .payment-method .btn-wrap .btn-form input {
  margin: 0;
}
#main .mypage-payment .payment-method .btn-wrap .btn-form.back {
  width: auto;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .btn-wrap .btn-form.back {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
    margin: 4vw 0;
  }
}
#main .mypage-payment .payment-method .btn-wrap .btn-form.back input {
  width: auto;
  height: auto;
  font-weight: 500;
  background-color: transparent;
  border: none;
}
#main .mypage-payment .payment-method .btn-wrap .btn-form.back input:hover {
  text-decoration: underline;
}
#main .mypage-payment .payment-method .btn-wrap .btn-form.confirm {
  width: auto;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .btn-wrap .btn-form.confirm {
    width: 100%;
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
    margin: 4vw 0;
  }
}
#main .mypage-payment .payment-method .btn-wrap .btn-form.confirm input {
  width: 280px;
  height: 60px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .btn-wrap .btn-form.confirm input {
    width: 100%;
  }
}
#main .mypage-payment .payment-method .btn-wrap .new-card {
  position: relative;
  margin: 10px 0 0;
  padding: 1rem 1.5rem 1rem 3rem;
  color: #fff;
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.1;
  background-color: #6e6e6e;
  border-radius: 6px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .btn-wrap .new-card {
    width: 100%;
    padding: 4vw 4vw;
    font-size: 3.2vw;
  }
}
#main .mypage-payment .payment-method .btn-wrap .new-card:hover {
  opacity: 0.8;
}
#main .mypage-payment .payment-method .btn-wrap .new-card::before, #main .mypage-payment .payment-method .btn-wrap .new-card::after {
  content: "";
  position: absolute;
  left: 20px;
  top: 50%;
  width: 12px;
  height: 2px;
  background: currentColor;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-radius: 1px;
}
@media screen and (max-width: 767px) {
  #main .mypage-payment .payment-method .btn-wrap .new-card::before, #main .mypage-payment .payment-method .btn-wrap .new-card::after {
    left: 12vw;
  }
}
#main .mypage-payment .payment-method .btn-wrap .new-card::after {
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}
#main .mypage-payment .payment-method .btn-wrap .back.text {
  margin: 4rem 0 2rem;
  color: #427fae;
  font-weight: 500;
}
#main .mypage-payment .payment-method .btn-wrap .back.text:hover {
  text-decoration: underline;
}
#main #mypage-form {
  margin: 0 0 60px;
  padding: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-form {
    margin: 0 0 40px;
    padding: 0 0 1rem;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *モーダル（カード情報入力）
 * *********************************************************************** */
#main.payment .modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 40px 20px;
  overflow: auto;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  z-index: 100;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container {
    padding: 10vw 4vw;
  }
}
#main.payment .modal-container:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
#main.payment .modal-container.active {
  opacity: 1;
  visibility: visible;
}
#main.payment .modal-container .modal-body {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: calc(100% - 2rem);
  width: 1000px;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body {
    max-width: calc(100% - 4vw);
  }
}
#main.payment .modal-container .modal-body .modal-close {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  top: -45px;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  z-index: 120;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body .modal-close {
    top: -10vw;
    right: 0;
    width: 10vw;
    height: 10vw;
  }
}
#main.payment .modal-container .modal-body .modal-close:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50%;
  height: 2px;
  background: #fff;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
          transform: translate(-50%, -50%) rotate(45deg);
}
#main.payment .modal-container .modal-body .modal-close:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50%;
  height: 2px;
  background: #fff;
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
          transform: translate(-50%, -50%) rotate(-45deg);
}
#main.payment .modal-container .modal-body .modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body .modal-close:hover {
    background-color: transparent;
  }
}
#main.payment .modal-container .modal-body .modal-content {
  position: relative;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  z-index: 110;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body .modal-content {
    padding: 4vw;
  }
}
#main.payment .modal-container .modal-body .modal-content h4 {
  width: 100%;
  margin: 0 0 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body .modal-content h4 {
    font-size: 4vw;
  }
}
#main.payment .modal-container .modal-body .modal-content form #entry-form {
  width: 980px;
  max-width: 100%;
  margin: 1rem auto;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body .modal-content form #entry-form {
    margin: 7vw 0;
  }
}
#main.payment .modal-container .modal-body .modal-content form #entry-form .tbl-entry input.check-default {
  -webkit-transform: scale(1.3);
          transform: scale(1.3);
}
#main.payment .modal-container .modal-body .modal-content form .btn-form.card {
  margin: 2rem 0 1rem;
}
#main.payment .modal-container .modal-body .modal-content .note-bid {
  width: 100%;
  margin: 1.5rem 0 0;
  padding: 0;
  font-size: 0.7rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main.payment .modal-container .modal-body .modal-content .note-bid {
    margin: 4vw 0 0;
    font-size: 2.7vw;
  }
}
/*# sourceMappingURL=mypage.css.map */