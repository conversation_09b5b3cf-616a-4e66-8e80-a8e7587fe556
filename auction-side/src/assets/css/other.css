@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* ---------------------------
 * *introduction
 * *---------------------------- */
#main .other {
  padding: 40px 1rem 80px;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main .other {
    padding: 2vw 4vw 7vw;
    font-size: 3.5vw;
  }
}
#main .other .container {
  width: 1000px;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main .other .container {
    width: 100%;
  }
}
#main .other .container .introduction p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #main .other .container .introduction p {
    font-size: 3.2vw;
  }
}

/* table
 * *========================================== */
.other table.tbl-otherItem {
  width: 100%;
  margin: 0;
  border-top: 1px solid #e9eaeb;
}
@media screen and (max-width: 767px) {
  .other table.tbl-otherItem {
    border: none;
  }
}
.other table.tbl-otherItem th, .other table.tbl-otherItem td {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9eaeb;
  vertical-align: top;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  .other table.tbl-otherItem th, .other table.tbl-otherItem td {
    width: 100%;
    display: block;
    font-size: 3.2vw;
    border: none;
  }
}
.other table.tbl-otherItem th {
  width: 25%;
  font-weight: 700;
  background-color: #F7F7F7;
}
@media screen and (max-width: 767px) {
  .other table.tbl-otherItem th {
    width: 100%;
    padding: 4vw 2vw;
  }
}
@media screen and (max-width: 767px) {
  .other table.tbl-otherItem td {
    padding: 4vw 2vw 6vw;
  }
}
.other table.tbl-otherItem td ul {
  margin: 0;
}
.other table.tbl-otherItem td ul li {
  line-height: 1.6;
}
.other table.tbl-otherItem td ul li + li {
  margin: 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  .other table.tbl-otherItem td ul li + li {
    margin: 2vw 0 0;
  }
}
.other table.tbl-otherItem td ul .decimal {
  list-style-type: decimal;
  padding: 0 0 0 1.3rem;
}
.other table.tbl-otherItem td ul .disc {
  list-style-type: disc;
  padding: 0 0 0 1.3rem;
}
.other table.tbl-otherItem td dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0.8rem 0 0;
  line-height: 1.6;
}
.other table.tbl-otherItem td dl dt {
  width: 5rem;
  margin: 0 0 0.8rem;
}
.other table.tbl-otherItem td dl dd {
  width: calc(100% - 5rem);
  margin: 0 0 0.8rem;
}
.other table.tbl-otherItem td p {
  margin: 0.8rem 0 0;
  line-height: 1.6;
}
.other table.tbl-otherItem td p .ma-0 {
  margin: 0;
}
.other table.tbl-otherItem td p .ma-l {
  margin: 2.6rem 0 0;
}
.other table.tbl-otherItem td p .ttl {
  font-weight: 500;
}

/* リスト
 * *========================================== */
/* ---------------------------
 * *DL リスト - 1
 * *---------------------------- */
dl.list_main {
  padding: 0 0 40px;
}
@media screen and (max-width: 767px) {
  dl.list_main {
    padding: 0 0 4vw;
  }
}
dl.list_main > dt {
  margin: 3.5rem 0 1rem;
  font-weight: 500;
  font-size: 1.3rem;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  dl.list_main > dt {
    margin: 7vw 0 2vw;
    font-size: 4vw;
  }
}
dl.list_main > dd {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  dl.list_main > dd {
    font-size: 3.2vw;
  }
}
dl.list_main > dd p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  dl.list_main > dd p {
    font-size: 3.2vw;
  }
}

dl.list_sub > dt {
  margin-top: 30px;
  font-weight: 700;
  text-indent: -2.15em;
  padding-left: 2.15em;
}
dl.list_sub > dt:first-of-type {
  margin-top: 5px;
}
dl.list_sub > dt span {
  display: inline-block;
  margin-right: 1em;
  font-weight: 700;
}
dl.list_sub > dd {
  padding-left: 2.15em;
  font-size: 17px;
}

/* ---------------------------
 * *OL リスト　（FAQ）
 * *----------------------------- */
.other {
  /*** 数字 ***/
}
.other ol {
  margin: 0.5rem 0;
}
@media screen and (max-width: 767px) {
  .other ol {
    margin: 2vw 0;
  }
}
.other ol li {
  font-size: 0.9rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  .other ol li {
    font-size: 3.2vw;
    line-height: 1.8;
  }
}
.other ol li span.label-s {
  position: relative;
  display: inline-block;
  width: 2.5rem;
}
@media screen and (max-width: 767px) {
  .other ol li span.label-s {
    width: 8vw;
  }
}
.other ol li span.label-s:after {
  position: absolute;
  top: 50%;
  right: 2px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: inline-block;
  content: ":";
}
.other ol li + li {
  margin: 0.3rem 0 0;
}
.other .list_nmb-o1 {
  list-style: disc;
  padding: 0 0 0 1.3rem;
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o1 {
    padding: 0 0 0 1.3rem;
  }
}
.other .list_nmb-o2 {
  list-style: circle;
  padding: 0 0 0 1.7rem;
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o2 {
    padding: 0 0 0 1.3rem;
  }
}
.other .list_nmb-o3 {
  list-style: none;
  padding: 0 0 0 1.3rem;
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o3 {
    padding: 0 0 0 5vw;
  }
}
.other .list_nmb-o3 li {
  text-indent: -1rem;
}
.other .list_nmb-o4 {
  width: calc(100% - 3.5rem);
  list-style: none;
  margin-left: auto;
  padding: 0 0 0 1.3rem;
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o4 {
    width: calc(100% - 8vw);
    padding: 0 0 0 7vw;
  }
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o4 li {
    text-indent: -2rem;
  }
}
.other .list_nmb-o4.paragraph {
  width: calc(100% - 2.5rem);
  margin-left: auto;
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o4.paragraph {
    width: calc(100% - 7.5vw);
  }
}
.other .list_nmb-o5 {
  list-style: decimal;
  padding: 0 0 0 1.7rem;
}
@media screen and (max-width: 767px) {
  .other .list_nmb-o5 {
    padding: 0 0 0 1.3rem;
  }
}

/* ---------------------------
 * *UL リスト - 1
 * *----------------------------- */
ul.list_nmb-u1 li {
  text-indent: -2.15em;
  padding-left: 2.15em;
}
ul.list_nmb-u1 li span {
  display: inline-block;
  margin-right: 1em;
}
@media screen and (max-width: 767px) {
  ul.list_nmb-u1 li + li {
    margin-top: 7px;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *会社概要
 * *********************************************************************** */
#main #profile {
  padding: 60px 2rem;
  font-size: 1rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #main #profile {
    padding: 40px 1rem;
  }
}
#main #profile span.enCoName {
  word-break: normal;
}
#main #rule {
  font-size: 1rem;
  line-height: 1.8;
  padding-bottom: 110px;
}
#main #rule p.intro_rule {
  margin-top: 70px;
  padding: 0 45px;
}
#main #rule p.additional {
  padding: 0 3rem;
}
@media screen and (max-width: 767px) {
  #main #rule p.additional {
    padding: 0;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *特定商取引に基づく表記
 * *********************************************************************** */
#main .other h3.ttl-kobutsu {
  margin: 4rem 0 1.5rem;
  padding: 0;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.2;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .other h3.ttl-kobutsu {
    margin: 12vw 0 4.5vw;
    padding: 15vw 0 0;
    font-size: 4.3vw;
    border-top: 1px solid #e9eaeb;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *プライバシーポリシー
 * *********************************************************************** */
#main .sign {
  margin: 1rem 0;
}
@media screen and (max-width: 767px) {
  #main .sign {
    margin: 2vw 0;
  }
}
#main .sign span {
  display: block;
  font-size: 0.9rem;
  text-align: right;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #main .sign span {
    font-size: 3.2vw;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *利用規約
 * *********************************************************************** */
#main .other .container .o-ttl {
  margin: 3rem 0 1rem;
  font-size: 1.6rem;
  font-weight: 500;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #main .other .container .o-ttl {
    font-size: 5vw;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *FAQ
 * *********************************************************************** */
/* ---------------------------
 * *アコーディオン
 * *----------------------------- */
#qa .menu-ttl {
  margin: 3rem 0.5rem 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #qa .menu-ttl {
    margin: 10vw 0 3vw;
    font-size: 4vw;
    text-align: center;
  }
}
#qa .menu-ttl.mt0 {
  margin-top: 0;
}
#qa .qa-wrapper-ac {
  padding: 0;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac {
    padding: 0;
  }
}
#qa .qa-wrapper-ac li {
  padding: 0;
}
#qa .qa-wrapper-ac li:hover {
  opacity: 1;
}
#qa .qa-wrapper-ac li:last-child dl {
  border-bottom: 1px solid #f8f8f8;
}
#qa .qa-wrapper-ac li dl {
  margin: 0 0 0.2rem;
  border-top: 1px solid #f8f8f8;
  border-left: 1px solid #f8f8f8;
  border-right: 1px solid #f8f8f8;
}
#qa .qa-wrapper-ac li dl dt {
  position: relative;
  display: block;
  padding: 1.1rem 4rem;
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.4;
  background-color: #f8f8f8;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt {
    padding: 3.5vw 3.4rem 3.5vw 3rem;
    font-size: 3.2vw;
    line-height: 1.5;
  }
}
#qa .qa-wrapper-ac li dl dt:hover, #qa .qa-wrapper-ac li dl dt.is-active {
  cursor: pointer;
  color: #427fae;
  background-color: #ecf2f7;
}
#qa .qa-wrapper-ac li dl dt:hover:before, #qa .qa-wrapper-ac li dl dt.is-active:before {
  color: #427fae;
}
#qa .qa-wrapper-ac li dl dt:before {
  position: absolute;
  top: 0.7rem;
  left: 0.5rem;
  content: "Q.";
  padding: 0.4rem 0.8rem;
  color: #333;
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt:before {
    top: 0.8rem;
    left: 0.4rem;
    padding: 0.2rem 0.5rem;
    font-size: 4vw;
  }
}
#qa .qa-wrapper-ac li dl dt .btn {
  position: absolute;
  top: calc(50% - 7px);
  right: 1.5rem;
  width: 12px;
  height: 12px;
  -webkit-transform-origin: center center;
          transform-origin: center center;
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt .btn {
    width: 10px;
    height: 10px;
    right: 1rem;
    top: calc(50% - 5px);
  }
}
#qa .qa-wrapper-ac li dl dt .btn:before, #qa .qa-wrapper-ac li dl dt .btn:after {
  content: "";
  background-color: #bdbdbd;
  border-radius: none;
  width: 12px;
  height: 2px;
  position: absolute;
  top: 5px;
  left: 0;
  -webkit-transform-origin: center center;
          transform-origin: center center;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt .btn:before, #qa .qa-wrapper-ac li dl dt .btn:after {
    width: 10px;
    top: calc(50% - 1px);
  }
}
#qa .qa-wrapper-ac li dl dt .btn:before {
  width: 2px;
  height: 12px;
  top: 0;
  left: 5px;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt .btn:before {
    height: 10px;
    left: calc(50% - 1px);
  }
}
#qa .qa-wrapper-ac li dl dt.is-active .btn {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
#qa .qa-wrapper-ac li dl dt.is-active .btn:before {
  content: none;
}
#qa .qa-wrapper-ac li dl dt::-webkit-details-marker {
  display: none;
}
#qa .qa-wrapper-ac li dl dd {
  display: none;
  padding: 1.1rem 1rem 1.1rem 4rem;
  background-color: #fdfdfd;
  border-bottom: 1px solid #f8f8f8;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dd {
    padding: 3.5vw 4vw 3.5vw 4vw;
  }
}
#qa .qa-wrapper-ac li dl dd p {
  position: relative;
  margin: 0;
  padding: 0 3rem 0 2.5rem;
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dd p {
    padding: 0 0 0 7vw;
    font-size: 3.2vw;
  }
}
#qa .qa-wrapper-ac li dl dd p:before {
  position: absolute;
  top: -0.2rem;
  left: 0;
  content: "A.";
  padding: 0.4rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dd p:before {
    top: -0.2vw;
    left: 0;
    padding: 0.5vw 0;
    font-size: 3.8vw;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *ご利用ガイド
 * *********************************************************************** */
#guide .other .container .guide-ttl {
  margin: 3rem 0 1rem;
  padding: 0.7rem 1rem;
  font-size: 1.2rem;
  font-weight: 500;
  text-align: left;
  background-color: #eee;
}
@media screen and (max-width: 767px) {
  #guide .other .container .guide-ttl {
    margin: 12vw 0 4vw;
    font-size: 5vw;
  }
}
#guide .other .container .guide-ttl:first-child {
  margin: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #guide .other .container .guide-ttl:first-child {
    margin: 0 0 4vw;
  }
}
#guide .other .container .guide-content dt {
  margin: 1rem 0 0;
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 1.1rem;
  line-height: 1.4;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #guide .other .container .guide-content dt {
    margin: 4vw 0 2vw;
    padding: 2vw 0;
    font-size: 4vw;
  }
}
#guide .other .container .guide-content dd {
  padding: 1rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #guide .other .container .guide-content dd {
    padding: 0 0 2vw;
    font-size: 3.5vw;
  }
}
#guide .other .container .guide-content dd p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #guide .other .container .guide-content dd p {
    font-size: 3.2vw;
  }
}
/*# sourceMappingURL=other.css.map */