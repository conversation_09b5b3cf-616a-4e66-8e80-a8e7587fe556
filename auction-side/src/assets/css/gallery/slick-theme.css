@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *Slider
 * *********************************************************************** */
.slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

/* Icons */
@font-face {
  font-family: "slick";
  font-weight: normal;
  font-style: normal;
  src: url("./fonts/slick.eot");
  src: url("./fonts/slick.eot?#iefix") format("embedded-opentype"), url("./fonts/slick.woff") format("woff"), url("./fonts/slick.ttf") format("truetype"), url("./fonts/slick.svg#slick") format("svg");
}
/* 商品詳細：メインギャラリー
 * *========================================== */
.my-gallery .slick-prev, .my-gallery .slick-next, .list-item-gallery .slick-prev, .list-item-gallery .slick-next {
  position: absolute;
  top: calc(50% - 25px);
  display: block;
  width: 50px;
  height: 50px;
  color: transparent;
  border-radius: 50px;
  background-color: rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
}
@media screen and (max-width: 767px) {
  .my-gallery .slick-prev, .my-gallery .slick-next, .list-item-gallery .slick-prev, .list-item-gallery .slick-next {
    top: calc(46vw - 25px);
    width: 10vw;
    height: 10vw;
    border-radius: 10vw;
    background-color: rgba(0, 0, 0, 0.2);
  }
}
.my-gallery .slick-prev:before, .my-gallery .slick-next:before, .list-item-gallery .slick-prev:before, .list-item-gallery .slick-next:before {
  content: "";
  width: 16px;
  height: 16px;
  border: 0;
  position: absolute;
  top: 50%;
  right: 50%;
  background: url(../../img/common/icn_arrow.svg) center center no-repeat;
}
@media screen and (max-width: 767px) {
  .my-gallery .slick-prev:before, .my-gallery .slick-next:before, .list-item-gallery .slick-prev:before, .list-item-gallery .slick-next:before {
    width: 3vw;
    height: 3vw;
    background: url(../../img/common/icn_arrow_w.svg) center center no-repeat;
  }
}
.my-gallery .slick-prev:hover, .my-gallery .slick-next:hover, .list-item-gallery .slick-prev:hover, .list-item-gallery .slick-next:hover {
  border-radius: 50px;
  background-color: rgba(0, 0, 0, 0.5);
}
@media screen and (max-width: 767px) {
  .my-gallery .slick-prev:hover, .my-gallery .slick-next:hover, .list-item-gallery .slick-prev:hover, .list-item-gallery .slick-next:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}
.my-gallery .slick-prev:hover:before, .my-gallery .slick-next:hover:before, .list-item-gallery .slick-prev:hover:before, .list-item-gallery .slick-next:hover:before {
  background: url(../../img/common/icn_arrow_w.svg) center center no-repeat;
}
.my-gallery .slick-prev:focus:not(:hover), .my-gallery .slick-next:focus:not(:hover), .list-item-gallery .slick-prev:focus:not(:hover), .list-item-gallery .slick-next:focus:not(:hover) {
  background-color: rgba(0, 0, 0, 0.15);
}
.my-gallery .slick-prev, .list-item-gallery .slick-prev {
  left: -25px !important;
}
@media screen and (max-width: 767px) {
  .my-gallery .slick-prev, .list-item-gallery .slick-prev {
    left: -10px !important;
  }
}
.my-gallery .slick-prev:hover:before, .list-item-gallery .slick-prev:hover:before {
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.my-gallery .slick-prev:before, .list-item-gallery .slick-prev:before {
  -webkit-transform: rotate(180deg) translate(-50%, 50%);
          transform: rotate(180deg) translate(-50%, 50%);
}
.my-gallery .slick-next, .list-item-gallery .slick-next {
  right: -25px !important;
}
@media screen and (max-width: 767px) {
  .my-gallery .slick-next, .list-item-gallery .slick-next {
    right: -10px !important;
  }
}
.my-gallery .slick-next:hover:before, .list-item-gallery .slick-next:hover:before {
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.my-gallery .slick-next:before, .list-item-gallery .slick-next:before {
  -webkit-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

.list-item-gallery .slick-next {
  right: 8px !important;
}

/* 商品詳細：サブギャラリー（サムネイル）
 * *========================================== */
.slider-nav .slick-prev, .slider-nav .slick-next {
  position: absolute;
  top: calc(50% - 15px);
  display: block;
  width: 34px;
  height: 34px;
  color: transparent;
  border-radius: 30px;
  background-color: rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
}
.slider-nav .slick-prev:before, .slider-nav .slick-next:before {
  content: "";
  width: 10px;
  height: 10px;
  border: 0;
  position: absolute;
  top: 50%;
  right: 50%;
  background: url(../../img/common/icn_arrow.svg) center center no-repeat;
}
.slider-nav .slick-prev:hover, .slider-nav .slick-next:hover {
  border-radius: 30px;
  background-color: rgba(0, 0, 0, 0.5);
}
.slider-nav .slick-prev:hover:before, .slider-nav .slick-next:hover:before {
  background: url(../../img/common/icn_arrow_w.svg) center center;
}
.slider-nav .slick-prev {
  left: -18px !important;
}
.slider-nav .slick-prev:hover:before {
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.slider-nav .slick-prev:before {
  -webkit-transform: rotate(180deg) translate(-50%, 50%);
          transform: rotate(180deg) translate(-50%, 50%);
}
.slider-nav .slick-next {
  right: -18px !important;
}
.slider-nav .slick-next:hover:before {
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.slider-nav .slick-next:before {
  -webkit-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

/* Dots
 * *========================================== */
.slick-dotted.slick-slider {
  margin-bottom: 30px;
}

#main .list-item .item-list ul.slick-dots {
  position: absolute;
  bottom: -25px;
  display: none !important;
  /*切り替えblock/none */
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}
#main .list-item .item-list ul.slick-dots li {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  background-color: transparent;
  cursor: pointer;
}
#main .list-item .item-list ul.slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  width: 20px;
  height: 20px;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}
#main .list-item .item-list ul.slick-dots li button:hover, #main .list-item .item-list ul.slick-dots li button:focus {
  outline: none;
}
#main .list-item .item-list ul.slick-dots li button:hover:before, #main .list-item .item-list ul.slick-dots li button:focus:before {
  opacity: 1;
}
#main .list-item .item-list ul.slick-dots li button:before {
  font-family: "slick";
  font-size: 6px;
  line-height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  content: "•";
  text-align: center;
  opacity: 0.25;
  color: black;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#main .list-item .item-list ul.slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}
/*# sourceMappingURL=slick-theme.css.map */