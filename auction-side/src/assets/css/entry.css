@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *会員登録
 * *********************************************************************** */
#main .assessment {
  width: 980px;
  max-width: 100%;
  margin: 0 auto 1rem;
  padding: 2rem;
  background-color: #ecf2f7;
}
@media screen and (max-width: 767px) {
  #main .assessment {
    width: 100%;
    padding: 7vw 4vw;
  }
}
#main .assessment .ttl-assessment-flow {
  text-align: center;
  margin: 0 0 1rem;
  font-size: 1.2rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main .assessment .ttl-assessment-flow {
    font-size: 4vw;
  }
}
#main .assessment .intro {
  margin: 0 0 2rem;
}
#main .assessment .intro p {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .assessment .intro p {
    font-size: 3vw;
    text-align: left;
  }
}
#main .assessment .cont-assessment-flow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 1rem 0;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main .assessment .cont-assessment-flow .box-flow {
  width: calc((100% - 60px) / 3);
  padding: 1.5rem 1.2rem;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .box-flow {
    width: 100%;
    padding: 5vw 6vw;
  }
}
#main .assessment .cont-assessment-flow .box-flow .pri-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 0 0 0.5rem;
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .num {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 1rem;
  font-family: YakuHanJP, "Noto Sans JP", Arial, sans-serif;
  background-color: #427fae;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .box-flow .pri-item .num {
    font-size: 5vw;
  }
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .num span {
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .box-flow .pri-item .num span {
    font-size: 3vw;
  }
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0 0 0 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail p {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1.3;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail p {
    font-size: 3.5vw;
  }
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .btn.assessment-info {
  width: 18px;
  min-width: 18px;
  height: 18px;
  margin: 0 0 0 5px;
  padding: 0;
  background-color: transparent;
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .btn.assessment-info img {
  width: 100%;
  height: auto;
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .tooltip {
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease 0.3s, -webkit-transform 0.3s ease 0.3s;
  transition: opacity 0.3s ease 0.3s, -webkit-transform 0.3s ease 0.3s;
  transition: opacity 0.3s ease 0.3s, transform 0.3s ease 0.3s;
  transition: opacity 0.3s ease 0.3s, transform 0.3s ease 0.3s, -webkit-transform 0.3s ease 0.3s;
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
  position: absolute;
  top: auto;
  bottom: 100%;
  max-width: 100%;
  padding: 4px 10px 4px;
  color: #fff;
  font-size: 0.7rem;
  white-space: normal;
  line-height: 1.4;
  background-color: #363c45;
  border-radius: 4px;
  z-index: 10;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .tooltip {
    bottom: 120%;
    left: auto;
    right: 0;
    font-size: 3vw;
  }
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .tooltip:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: #363c45 transparent transparent transparent;
}
#main .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .tooltip.show {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
  -webkit-transition-delay: 0.3s;
          transition-delay: 0.3s;
}
#main .assessment .cont-assessment-flow .box-flow .ann {
  color: #ff0000;
  font-size: 0.6rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .box-flow .ann {
    font-size: 2.5vw;
  }
}
#main .assessment .cont-assessment-flow .arrow-assessment-flow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 30px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .arrow-assessment-flow {
    width: 100%;
    height: 24px;
  }
}
#main .assessment .cont-assessment-flow .arrow-assessment-flow span {
  display: inline-block;
  width: 14px;
  height: 20px;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .arrow-assessment-flow span {
    width: 20px;
    height: 14px;
    -webkit-transform: translateY(-25%);
            transform: translateY(-25%);
  }
}
#main .assessment .cont-assessment-flow .arrow-assessment-flow span::after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #427fae;
}
@media screen and (max-width: 767px) {
  #main .assessment .cont-assessment-flow .arrow-assessment-flow span::after {
    border-top: 14px solid #427fae;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-bottom: none;
  }
}
#main .assessment .btn-wrap {
  width: 100%;
  margin: 2rem 0 1rem;
  text-align: center;
}
#main .assessment .btn-wrap button.dl-doc {
  width: auto;
  height: 60px;
  margin: 0 auto;
  padding: 0 3rem;
  background-color: #333;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main .assessment .btn-wrap button.dl-doc {
    width: 100%;
    height: 14vw;
  }
}
#main .assessment .btn-wrap button.dl-doc span {
  position: relative;
  display: inline-block;
  padding: 0 26px 0 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .assessment .btn-wrap button.dl-doc span {
    padding: 0 6vw 0 0;
    font-size: 3.8vw;
  }
}
#main .assessment .btn-wrap button.dl-doc span::after {
  position: absolute;
  top: 3px;
  right: 0;
  content: "";
  display: inline-block;
  background: url(../img/common/icn_download_w.svg) center no-repeat;
  background-size: 17px auto;
  width: 18px;
  height: 18px;
}
@media screen and (max-width: 767px) {
  #main .assessment .btn-wrap button.dl-doc span::after {
    top: 1vw;
    background-size: 4vw auto;
    width: 4vw;
    height: 4vw;
  }
}
#main #entry-form {
  width: 980px;
  max-width: 100%;
  margin: 60px auto 2rem;
  padding: 60px 2rem 60px;
  background-color: #f8f8f8;
}
@media screen and (max-width: 767px) {
  #main #entry-form {
    max-width: 100%;
    margin: 4vw 0;
    padding: 4vw 0;
    background-color: transparent;
  }
}
#main #entry-form .comp-msg {
  width: 100%;
  margin: 0 0 40px;
  font-size: 1rem;
  font-weight: 700;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #entry-form .comp-msg {
    margin: 0 0 7vw;
    font-size: 3vw;
    text-align: left;
  }
}
#main #entry-form .entry-form-info {
  text-align: center;
  font-weight: 700;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form .entry-form-info {
    text-align: left;
    font-size: 3vw;
  }
}
#main #entry-form .entry-form-info em.req {
  margin: 0 0.2rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #entry-form .entry-form-info em.req {
    margin: 0 0.5vw 0 0;
    text-align: left;
    font-size: 3vw;
  }
}
#main #entry-form em.req {
  display: inline-block;
  width: auto;
  height: 20px;
  color: #ff0000;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  line-height: 19px;
}
@media screen and (max-width: 767px) {
  #main #entry-form em.req {
    font-size: 3vw;
  }
}
#main #entry-form p.entry-form-info + table.tbl-entry {
  margin-top: 40px;
}
#main #entry-form table.tbl-entry {
  width: 100%;
  margin: 0 auto;
}
#main #entry-form table.tbl-entry tr {
  padding: 0;
}
#main #entry-form table.tbl-entry tr th {
  position: relative;
  width: 280px;
  padding: 15px 32px;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.2;
  vertical-align: middle;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr th {
    width: 230px;
    min-width: 190px;
    padding: 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th {
    display: block;
    width: 100%;
    padding: 0 0.5vw;
    font-size: 3vw;
  }
}
#main #entry-form table.tbl-entry tr th em.req {
  position: absolute;
  top: 50%;
  right: 10px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th em.req {
    position: static;
    right: auto;
    -webkit-transform: translateY(0);
            transform: translateY(0);
    margin: 0 1vw;
  }
}
#main #entry-form table.tbl-entry tr th.top-justified {
  padding: 18px 32px 15px;
  vertical-align: top;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr th.top-justified {
    padding: 18px 1rem 15px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th.top-justified {
    padding: 0 0.5vw;
  }
}
#main #entry-form table.tbl-entry tr th.top-justified em.req {
  position: absolute;
  top: calc(0.5rem + 15px + 3px);
  right: 10px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th.top-justified em.req {
    top: 1.8vw;
    right: 1vw;
  }
}
#main #entry-form table.tbl-entry tr th.top-justified.pt-add {
  padding-top: calc(15px + 1rem + 0.5rem);
}
#main #entry-form table.tbl-entry tr th.top-justified.pt-add em.req {
  top: calc(0.5rem + 15px + 3px + 1.5rem);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th.post-code {
    width: 38vw;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th.post-code em.req {
    right: 7vw;
  }
}
#main #entry-form table.tbl-entry tr td {
  width: calc(100% - 1rem);
  padding: 15px 0;
  position: relative;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td {
    display: block;
    width: 100%;
    padding: 1vw 0 5vw;
    font-size: 3.5vw;
    line-height: 1.3;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.agree {
    padding: 2vw 2vw 1vw;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.upload {
    padding: 1vw 0 10vw;
  }
}
#main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  gap: 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap {
    gap: 3vw;
  }
}
#main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .upload-label {
  position: relative;
  display: inline-block;
  width: 160px;
  padding: 10px 16px 12px;
  background-color: #a5a5a5;
  border-radius: 8px;
  cursor: pointer;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .upload-label {
    width: 41vw;
    padding: 3vw 4vw 4vw;
  }
}
#main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .upload-label:after {
  position: absolute;
  top: calc(50% - 12px);
  right: 15px;
  content: "";
  display: inline-block;
  background: url(../img/common/icn_upload_doc_w.svg) center no-repeat;
  background-size: 18px auto;
  width: 18px;
  height: 24px;
}
#main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .upload-label:hover {
  background-color: #ccc;
}
#main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .upload-label span {
  display: inline-block;
  width: 100%;
  color: #fff;
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .upload-label span {
    font-size: 3.2vw;
  }
}
#main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .ipt-file-txt {
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.upload .upload-cont-wrap .ipt-file-txt {
    font-size: 3vw;
  }
}
#main #entry-form table.tbl-entry tr td.upload .att {
  padding: 0.5rem 0 0 0;
}
#main #entry-form table.tbl-entry tr td.upload .att p {
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.upload .att p {
    font-size: 2.5vw;
  }
}
#main #entry-form table.tbl-entry tr td.upload .att p:before {
  display: inline-block;
  content: "※";
}
#main #entry-form table.tbl-entry tr td p.privacy-link {
  margin-top: -20px;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td p.privacy-link {
    margin: 0;
    padding: 0 0 0 6vw;
    font-size: 2.8vw;
    line-height: 1.4;
  }
}
#main #entry-form table.tbl-entry tr td p.privacy-link a {
  color: #427fae;
  font-weight: 500;
}
#main #entry-form table.tbl-entry tr td p.privacy-link a:hover {
  text-decoration: underline;
  opacity: 1;
}
#main #entry-form table.tbl-entry tr td .checkbox-parts {
  font-size: 1.1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .checkbox-parts {
    font-size: 3.8vw;
  }
}
#main #entry-form table.tbl-entry tr td .ipt-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .ipt-wrap {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
#main #entry-form table.tbl-entry tr td .ipt-wrap .select-wrap {
  position: relative;
}
#main #entry-form table.tbl-entry tr td .ipt-wrap .select-wrap:after {
  position: absolute;
  content: "";
  right: 15px;
  top: 50%;
  width: 4px;
  height: 4px;
  border-right: 2px solid #bcbcbc;
  border-bottom: 2px solid #bcbcbc;
  -webkit-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}
#main #entry-form table.tbl-entry tr td .ipt-wrap .select-wrap select.iptW-S {
  width: 140px;
  height: 54px;
  padding: 11px 30px 11px 20px;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
#main #entry-form table.tbl-entry tr td .ipt-wrap .txt-obj {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 54px;
  padding: 0 0.5rem;
}
#main #entry-form table.tbl-entry tr td .ipt-wrap input.zip-search {
  width: 60px;
  height: 54px;
  margin: 0 0 0 15px;
  padding: 0.5rem;
  color: #fff;
  font-size: 0.8rem;
  font-weight: 600;
  background-color: #427fae;
  border-color: #427fae;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .ipt-wrap input.zip-search {
    width: 14vw;
    height: 12vw;
    margin: 0 0 0 2vw;
    padding: 2vw;
    font-size: 3.2vw;
  }
}
#main #entry-form table.tbl-entry tr td .ipt-wrap input.zip-search:hover {
  opacity: 0.8;
}
#main #entry-form table.tbl-entry tr td .ipt-wrap span.int-no {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 55px;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-right: none;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .ipt-wrap span.int-no {
    width: 16vw;
    font-size: 3.5vw;
  }
}
#main #entry-form table.tbl-entry tr td .ipt-wrap span.ipt-rule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 0.7rem;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .ipt-wrap span.ipt-rule {
    width: 100%;
    margin: 1vw 0 0;
    padding: 0 1vw;
    font-size: 2.5vw;
  }
}
#main #entry-form table.tbl-entry tr td .ipt-wrap span.ipt-rule.kome {
  padding-left: 0;
  text-indent: 0rem;
}
#main #entry-form table.tbl-entry tr td .ipt-wrap span.ipt-rule.kome:before {
  content: "※";
}
#main #entry-form table.tbl-entry tr td .select-style {
  width: 460px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .select-style {
    width: 100%;
    max-width: 100%;
  }
}
#main #entry-form table.tbl-entry tr td input.iptW-M {
  width: 460px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr td input.iptW-M {
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td input.iptW-M {
    width: 100%;
    max-width: 100%;
  }
}
#main #entry-form table.tbl-entry tr td input.iptW-MM {
  width: 300px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr td input.iptW-MM {
    width: 305px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td input.iptW-MM {
    width: calc(100% - 55px);
    width: calc(100% - 55px);
  }
}
#main #entry-form table.tbl-entry tr td input.iptW-S {
  width: 140px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td input.iptW-S {
    width: 38vw;
  }
}
#main #entry-form table.tbl-entry tr td input.ime-dis {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td input.ime-dis {
    width: calc(100% - 16vw);
  }
}
#main #entry-form table.tbl-entry tr td input.card-number {
  word-spacing: 0.5em;
}
#main #entry-form table.tbl-entry tr td textarea {
  width: 100%;
  height: 200px;
  resize: vertical;
}
#main #entry-form table.tbl-entry tr td .ipt-file-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .ipt-file-box {
    display: block;
  }
}
#main #entry-form table.tbl-entry tr td .ipt-file-box label {
  margin-right: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .ipt-file-box label {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
#main #entry-form table.tbl-entry tr td .file-up-att {
  margin-top: -20px;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .file-up-att {
    margin-top: -40px;
  }
}
#main #entry-form table.tbl-entry tr.vertically-centered th {
  padding: 15px 32px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.vertically-centered th {
    padding: 0 0 1vw;
  }
}
#main #entry-form table.tbl-entry tr.vertically-centered td {
  padding: 15px 0;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.vertically-centered td {
    padding: 0 0 5vw;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.vertically-centered td .nickname-label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    height: 12vw;
    padding: 0 2vw;
    font-size: 3.5vw;
    background-color: #f8f8f8;
    border-radius: 4px;
  }
}
#main #entry-form table.tbl-entry tr.att-use {
  display: none;
}
#main #entry-form table.tbl-entry tr.att-use th {
  height: 1px;
}
#main #entry-form table.tbl-entry tr.agree-box td {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.agree-box td {
    padding: 1vw 5vw 1vw 2vw;
  }
}
#main #entry-form table.tbl-entry tr.agree-box td label {
  height: 1.4rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.agree-box td label {
    height: calc(3.8vw + 3px);
  }
}
#main #entry-form table.tbl-entry tr.agree-box td .ann {
  margin: 0.5rem 0 0;
  font-size: 0.8rem;
}
#main #entry-form .btn-form {
  margin-top: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#main.comp p.comp-msg {
  margin: 0 0 60px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main.comp p.comp-msg {
    margin: 0 0 40px;
    text-align: left;
    font-size: 17px;
  }
}

/* ---------------------------
 * *会員登録 入力確認
 * *----------------------------- */
#entry-confirm #main #entry-form {
  padding: 60px 4rem 60px;
}
@media screen and (max-width: 767px) {
  #entry-confirm #main #entry-form {
    padding: 3vw 0;
  }
}
#entry-confirm #main #entry-form table.tbl-entry tr {
  border-bottom: 1px solid #e9eaeb;
}
#entry-confirm #main #entry-form table.tbl-entry tr:last-child {
  border-bottom: none;
}
#entry-confirm #main #entry-form table.tbl-entry tr th {
  padding: 20px 1rem;
}
@media screen and (max-width: 767px) {
  #entry-confirm #main #entry-form table.tbl-entry tr th {
    padding: 5vw 0 1vw;
  }
}
#entry-confirm #main #entry-form table.tbl-entry tr td {
  padding: 20px 1rem;
}
@media screen and (max-width: 767px) {
  #entry-confirm #main #entry-form table.tbl-entry tr td {
    padding: 0 0 5vw;
  }
}

/* ---------------------------
 * *2段階認証
 * *----------------------------- */
#verify-form {
  padding: 20px 0 100px;
}
@media screen and (max-width: 767px) {
  #verify-form {
    padding: 0;
  }
}
#verify-form .panel {
  width: 800px;
  max-width: 100%;
  margin: 0 auto;
  padding: 4rem 2rem 3.5rem;
  background-color: #f8f8f8;
  border-radius: 0;
}
@media screen and (max-width: 767px) {
  #verify-form .panel {
    padding: 12vw 4vw 13vw;
  }
}
#verify-form .panel .desc {
  width: 100%;
  margin: 0;
  line-height: 1.8;
  text-align: center;
  word-break: break-all;
}
@media screen and (max-width: 767px) {
  #verify-form .panel .desc {
    font-size: 3.2vw;
    text-align: left;
  }
}
#verify-form .panel .desc span {
  display: inline-block;
  margin: 0.5rem 0 0;
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #verify-form .panel .desc span {
    margin: 2vw 0 0;
    font-size: 4.6vw;
  }
}
#verify-form .panel .code-group {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 56px 12px 56px 12px 56px 12px 56px 12px 56px 12px 56px;
  grid-template-columns: repeat(6, 56px);
  gap: 12px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 2rem 0;
}
@media screen and (max-width: 767px) {
  #verify-form .panel .code-group {
    -ms-grid-columns: minmax(12vw, 1fr) 1.5vw minmax(12vw, 1fr) 1.5vw minmax(12vw, 1fr) 1.5vw minmax(12vw, 1fr) 1.5vw minmax(12vw, 1fr) 1.5vw minmax(12vw, 1fr);
    grid-template-columns: repeat(6, minmax(12vw, 1fr));
    gap: 1.5vw;
  }
}
#verify-form .panel .code-group .digit {
  width: 56px;
  height: 64px;
  text-align: center;
  font-size: 28px;
  letter-spacing: 0.02em;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  outline: none;
  caret-color: transparent;
  -webkit-transition: border-color 0.15s, -webkit-box-shadow 0.15s, -webkit-transform 0.08s;
  transition: border-color 0.15s, -webkit-box-shadow 0.15s, -webkit-transform 0.08s;
  transition: box-shadow 0.15s, border-color 0.15s, transform 0.08s;
  transition: box-shadow 0.15s, border-color 0.15s, transform 0.08s, -webkit-box-shadow 0.15s, -webkit-transform 0.08s;
}
@media screen and (max-width: 767px) {
  #verify-form .panel .code-group .digit {
    width: 100%;
    height: 14vw;
    font-size: 6vw;
    border-radius: 2vw;
  }
}
#verify-form .panel .code-group .digit:focus {
  border-color: #427fae;
  -webkit-box-shadow: 0 0 0 2px rgba(90, 169, 255, 0.35) inset;
          box-shadow: 0 0 0 2px rgba(90, 169, 255, 0.35) inset;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
#verify-form .panel .code-group .digit::-webkit-outer-spin-button, #verify-form .panel .code-group .digit::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
#verify-form .panel .code-group .digit[type=number] {
  -moz-appearance: textfield;
}
#verify-form .panel .btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#verify-form .panel .btn-wrap button {
  width: 280px;
  height: 60px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: none;
  border-radius: 60px;
  padding: 0;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  background: #427fae;
  color: #fff;
  -webkit-transition: opacity 0.15s, -webkit-transform 0.08s, -webkit-filter 0.15s;
  transition: opacity 0.15s, -webkit-transform 0.08s, -webkit-filter 0.15s;
  transition: transform 0.08s, filter 0.15s, opacity 0.15s;
  transition: transform 0.08s, filter 0.15s, opacity 0.15s, -webkit-transform 0.08s, -webkit-filter 0.15s;
}
@media screen and (max-width: 767px) {
  #verify-form .panel .btn-wrap button {
    width: 100%;
    height: 15vw;
    font-size: 3.9vw;
  }
}
#verify-form .panel .btn-wrap button:disabled {
  opacity: 0.55;
  cursor: not-allowed;
  -webkit-box-shadow: none;
          box-shadow: none;
}
#verify-form .panel .btn-wrap button:not(:disabled):active {
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
#verify-form .panel .btn-wrap .text {
  display: inline-block;
  margin: 1.5rem auto 1rem;
  color: #427fae;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #verify-form .panel .btn-wrap .text {
    font-size: 3.5vw;
  }
}
#verify-form .panel .btn-wrap .text:hover {
  text-decoration: underline;
}

/* ---------------------------
 * *エラー
 * *----------------------------- */
.err-message {
  padding: 1rem;
  color: #ff0000;
  font-size: 0.9rem;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  .err-message {
    font-size: 3.2vw;
  }
}
.err-message p {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  .err-message p {
    font-size: 3.2vw;
  }
}
/*# sourceMappingURL=entry.css.map */