@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *お知らせ
 * *********************************************************************** */
/* Newslist
 * *========================================== */
#main .news {
  width: 100%;
  margin: 0;
}
#main .news .container {
  width: 100%;
  padding: 0;
}
#main .news .container .info-item {
  width: 1080px;
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  #main .news .container .info-item {
    -ms-grid-column: 1;
    grid-column: 1;
    -ms-grid-row: 2;
    grid-row: 2;
    padding: 4vw;
  }
}
#main .news .container .info-item li {
  padding: 0;
  border-bottom: 1px solid #ccc;
}
#main .news .container .info-item li:first-child {
  border-top: 1px solid #333;
}
#main .news .container .info-item li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 1.4rem 1.5rem;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main .news .container .info-item li a {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 5vw 0 6vw;
  }
}
#main .news .container .info-item li a:hover {
  background-color: #f8f8f8;
}
#main .news .container .info-item li a span {
  font-size: 1rem;
  line-height: 1.6;
  letter-spacing: 0;
}
@media screen and (max-width: 767px) {
  #main .news .container .info-item li a span {
    font-size: 3.2vw;
    font-size-line-height: 1.4;
  }
}
#main .news .container .info-item li a span.notice-day {
  width: 110px;
  display: inline-block;
  margin-right: 1.5rem;
  color: #9d9d9d;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  #main .news .container .info-item li a span.notice-day {
    width: 100%;
    display: block;
    margin: 0 0 1vw;
  }
}
#main .news .container .info-item li a span.notice-title {
  width: calc(100% - 110px);
}
@media screen and (max-width: 767px) {
  #main .news .container .info-item li a span.notice-title {
    width: 100%;
  }
}
#main .news .container .news-detail-contents {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 0 100px;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents {
    padding: 0 0 10vw;
  }
}
#main .news .container .news-detail-contents .news-head {
  padding: 2rem 1rem 2.5rem;
  background-color: #ecf2f7;
  border-bottom: 1px solid #ecf2f7;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .news-head {
    padding: 5vw 4vw 7vw;
  }
}
#main .news .container .news-detail-contents .news-head h2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  width: 1000px;
  max-width: 100%;
  margin: 1.5rem auto;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .news-head h2 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin: 0;
    text-align: left;
  }
}
#main .news .container .news-detail-contents .news-head h2 span {
  display: block;
  width: 100%;
  font-weight: 600;
}
#main .news .container .news-detail-contents .news-head h2 span.tab.new {
  width: 100px;
  margin: 0 1rem 0 0;
  color: #e98181;
  font-weight: 700;
}
#main .news .container .news-detail-contents .news-head h2 span.title {
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .news-head h2 span.title {
    font-size: 4vw;
    line-height: 1.4;
  }
}
#main .news .container .news-detail-contents .news-head h2 span.date {
  margin: 0 0 0.3rem;
  color: #9d9d9d;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .news-head h2 span.date {
    margin: 0 0 1vw;
    font-size: 3.2vw;
  }
}
#main .news .container .news-detail-contents .news-article {
  padding: 2.5rem 1rem;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .news-article {
    padding: 5vw 4vw;
  }
}
#main .news .container .news-detail-contents .news-article .article-contents {
  width: 1000px;
  max-width: 100%;
  margin: 0 auto;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .news-article .article-contents {
    font-size: 3.2vw;
  }
}
#main .news .container .news-detail-contents .back-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .back-list {
    padding: 4vw 7vw;
  }
}
#main .news .container .news-detail-contents .back-list .btn.back {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 280px;
  height: 60px;
  border-radius: 50px;
  border: 1px solid #427fae;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .back-list .btn.back {
    width: 100%;
    height: 15vw;
  }
}
#main .news .container .news-detail-contents .back-list .btn.back span {
  color: #427fae;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .news .container .news-detail-contents .back-list .btn.back span {
    font-size: 3.9vw;
  }
}
/*# sourceMappingURL=news.css.map */