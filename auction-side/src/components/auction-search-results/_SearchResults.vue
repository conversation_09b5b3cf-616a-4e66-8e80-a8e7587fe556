<script setup lang="ts">
  import type {ProductListHandlers} from '@/components/common/auction/types'
import {useViewMode} from '@/components/common/auction/useViewMode'
import BidConfirmModal from '@/components/common/BidConfirmModal.vue'
import BreadCrumb from '@/components/common/BreadCrumb.vue'
import type {AuctionClassification} from '@/composables/_type.ts'
import useFavorite from '@/composables/favorite'
import useSearchProducts from '@/composables/searchProducts'
import useApi from '@/composables/useApi'
import {useLanguageRefetch} from '@/composables/useLanguageRefetch'
import {CLASSIFICATIONS} from '@/defined/const'
import {useTypedI18n} from '@/language'
import {useBidConfirmStore} from '@/stores/bidConfirm'
import {useSearchResultStore} from '@/stores/search-results.js'
import {useTenantSettingsStore} from '@/stores/tenantSettings'
import {storeToRefs} from 'pinia'
import {computed, defineAsyncComponent, onBeforeMount} from 'vue'
import SearchResultFilterBox from './SearchResultFilterBox.vue'

  const PanelAuctionItem = defineAsyncComponent(
    () => import('@/components/common/auction/PanelAuctionItem.vue')
  )
  const RowBidAuctionItem = defineAsyncComponent(
    () => import('@/components/common/auction/RowBidAuctionItem.vue')
  )

  const {viewMode, toggleView} = useViewMode()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const store = useSearchResultStore()
  const tenantSettingsStore = useTenantSettingsStore()
  const bid = useBidConfirmStore()
  const {productList, setCurrentPage, setItemsPerPage,} = store
  const {t: translate} = useTypedI18n()
  const {
    getConstants,
    search,
    downloadCsv,
    } = useSearchProducts()
  const {refetchOnLanguageChange} = useLanguageRefetch()
  const {toggleFavorite} = useFavorite()

  const totalCount = computed(() => store.totalCount)
  const currentPage = computed(() => store.currentPage)
  const itemsPerPage = computed(() => store.itemsPerPage)

  // Get search result view mode from tenant settings (using storeToRefs to maintain reactivity)
  const {searchResultViewMode, isLoading: tenantSettingsLoading, isLoaded: tenantSettingsLoaded} = storeToRefs(tenantSettingsStore)

  const items = computed(() => {
    return productList.all || []
  })

  const filteredItems = computed(() => {
    const classificationNumber =
      store.selectedAucClassification === CLASSIFICATIONS.ASCENDING ? 1 : 2
    return items.value.filter(item => item.auction_classification === classificationNumber)
  })

  const viewOnlyItems = computed(() => {
    const filtered = items.value.filter(item => item.free_field.product_name)
    return filtered
  })
  const biddableItems = computed(() => {
    return items.value
  })
  setCurrentPage(1)
  setItemsPerPage(20) // This should match the API limit parameter

  // Update totalCount to reflect filtered items
  // TODO: This should be based on filtered items, use in future
  const filteredTotalCount = computed(() => {
    return filteredItems.value.length
  })

  const isAscendingAuction = computed(
    () => store.selectedAucClassification === CLASSIFICATIONS.ASCENDING
  )

  const totalPages = computed(() => {
    return Math.ceil(totalCount.value / itemsPerPage.value)
  })

  const pageNumbers = computed(() => {
    const total = totalPages.value
    const current = currentPage.value
    const pages: number[] = []

    if (total <= 7) {
      // Show all pages if total is 7 or less
      for (let i = 1; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // Show pages around current page
      if (current <= 4) {
        // Show first 5 pages + ... + last page
        for (let i = 1; i <= 5; i++) {
          pages.push(i)
        }
        if (total > 6) {
          pages.push(-1) // Ellipsis indicator
          pages.push(total)
        }
      } else if (current >= total - 3) {
        // Show first page + ... + last 5 pages
        pages.push(1)
        if (total > 6) {
          pages.push(-1) // Ellipsis indicator
        }
        for (let i = total - 4; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // Show first + ... + current-1, current, current+1 + ... + last
        pages.push(1)
        pages.push(-1) // Ellipsis indicator
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i)
        }
        pages.push(-1) // Ellipsis indicator
        pages.push(total)
      }
    }

    return pages
  })

  const canGoPrevious = computed(() => currentPage.value > 1)
  const canGoNext = computed(() => currentPage.value < totalPages.value)

  const currentItemsStart = computed(() => {
    return (currentPage.value - 1) * itemsPerPage.value + 1
  })

  const currentItemsEnd = computed(() => {
    return Math.min(currentPage.value * itemsPerPage.value, totalCount.value)
    // return Math.min(currentPage.value * itemsPerPage, filteredTotalCount.value)
  })

  // Pagination navigation functions
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
      setCurrentPage(page)
      console.log(`Navigating to page ${page}`)
    }
  }

  const goToPreviousPage = () => {
    if (canGoPrevious.value) {
      goToPage(currentPage.value - 1)
    }
  }

  const goToNextPage = () => {
    if (canGoNext.value) {
      goToPage(currentPage.value + 1)
    }
  }

  // PanelAuctionItem handlers
  const handlers: ProductListHandlers = {
    onFavoriteToggle: async (exhibitionItemNo: string, currentFavorited: boolean) => {
      try {
        await toggleFavorite(exhibitionItemNo, currentFavorited)
      } catch (error) {
        console.error('Failed to toggle favorite:', error)
      }
    },
    onRefresh: async () => {
      await searchOnLoad()
    },
  }

  const searchOnLoad = async () => {
    try {
      const params = {
        unSoldOut: false,
        bidding: false,
        limit: store.itemsPerPage,
        offset: (store.currentPage - 1) * store.itemsPerPage,
        auction_classification: store.selectedAucClassification,
        window_id:
          searchResultViewMode.value === 'panel'
            ? 'items_panel'
            : searchResultViewMode.value === 'row'
              ? 'items_list'
              : null,
      }

      await search(params) // public/search-auction-items
    } catch (error) {
      console.error('Error fetching products:', error)
      parseHtmlResponseError(error)
    }
  }

  onBeforeMount(async () => {
    try {
      store.resetSearchFilters()
      store.resetProductList()
      await Promise.all([getConstants(), tenantSettingsStore.fetchTenantSettings()])
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
    }
  })

  const handleAuctionTypeChange = async (classificationType: AuctionClassification) => {
    const classificationValue =
      classificationType === 'ascending' ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
    store.selectedAucClassification = classificationValue
    setCurrentPage(1) // Reset to first page
    await searchOnLoad()
  }

  const refreshList = async classification => {
    await searchOnLoad()
  }

  const handleCsvDownload = async event => {
    event.preventDefault()
    event.stopPropagation()
    const params = {
      unSoldOut: false,
      favorite: false,
      bidding: false,
      auction_classification: store.selectedAucClassification,
    }
    await downloadCsv(params)
  }

  // Set up language change watcher
  refetchOnLanguageChange(searchOnLoad)
</script>

<template>
  <main id="main">
    <BreadCrumb :customTitle="translate('PRODUCT_LIST')" />

    <div class="auction-type-label">
      <div class="type-tab-wrap">
        <span v-if="store.selectedAucClassification === CLASSIFICATIONS.ASCENDING" class="active">{{
          translate('ASCENDING_AUCTION')
        }}</span>
        <a v-else @click="handleAuctionTypeChange('ascending')" href="#">{{
          translate('ASCENDING_AUCTION')
        }}</a>
        <span v-if="store.selectedAucClassification === CLASSIFICATIONS.SEALED" class="active">{{
          translate('SEALED_AUCTION')
        }}</span>
        <a v-else @click="handleAuctionTypeChange('sealed')" href="#">{{
          translate('SEALED_AUCTION')
        }}</a>
      </div>
    </div>

    <h2 class="page-ttl list">
      <div class="ttl">{{ translate('PRODUCT_LIST') }}</div>
      <p class="sub">Item</p>
    </h2>

    <SearchResultFilterBox />
    <section id="list" class="list-item">
      <div class="container">
        <!-- リストタイプ A: Panel mode with no bid column and display toggle -->
        <template v-if="searchResultViewMode === 'panel'">
          <!-- display-option -->
          <div class="display-option">
            <div class="refine">
              <div class="sorting">
                <button class="menu_trigger">
                  <span class="option_selected">{{
                    translate('SEARCH_RESULTS_SORT_RECOMMENDED')
                  }}</span>
                </button>
                <ul class="sorting_panel">
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_RECOMMENDED') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_NEWEST') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_TIME_REMAINING_ASC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_TIME_REMAINING_DESC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_PRICE_ASC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_PRICE_DESC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_BID_COUNT_DESC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_BID_COUNT_ASC') }}</a>
                  </li>
                </ul>
              </div>
              <div class="check-onsale">
                <div class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" /><label
                    for="checkbox"
                    >{{ translate('SEARCH_RESULTS_ON_SALE_ONLY') }}</label
                  >
                </div>
              </div>
            </div>
            <div class="switch">
              <p class="dl">
                <a href="./" @click.prevent="handleCsvDownload($event)"
                  ><span>{{ translate('SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_HANDOVER') }}</span></a
                >
              </p>
              <div class="number-switch">
                <p class="label">{{ translate('SEARCH_RESULTS_DISPLAY_COUNT') }}</p>
                <div class="num">
                  <button class="btn" :class="{'is-active': itemsPerPage == 20}" @click="setItemsPerPage(20)">{{ translate('SEARCH_RESULTS_20_ITEMS') }}</button>
                  <button class="btn" :class="{'is-active': itemsPerPage == 50}" @click="setItemsPerPage(50)">{{ translate('SEARCH_RESULTS_50_ITEMS') }}</button>
                  <button class="btn" :class="{'is-active': itemsPerPage == 100}" @click="setItemsPerPage(100)">{{ translate('SEARCH_RESULTS_100_ITEMS') }}</button>
                </div>
              </div>
              <div class="display-switch">
                <p>
                  <button
                    :class="['btn', 'panel', {'is-active': viewMode === 'panel'}]"
                    @click="toggleView('panel')"
                  ></button>
                </p>
                <p>
                  <button
                    :class="['btn', 'row', {'is-active': viewMode === 'row'}]"
                    @click="toggleView('row')"
                  ></button>
                </p>
              </div>
            </div>
          </div>

          <!-- 入札不可（入札欄無し）のオークション -->
          <div v-if="viewOnlyItems.length > 0" :class="['item-list', viewMode]">
            <ul>
              <PanelAuctionItem
                v-for="(item, index) in viewOnlyItems"
                :key="String(item.item_no) + String(item.lot_id)"
                :item="item"
                :view-mode="viewMode"
                :handlers="handlers"
                custom-classes=""
              />
            </ul>
          </div>
        </template>

        <!-- リストタイプ B: Row mode with bid column -->
        <template v-if="searchResultViewMode === 'row'">
          <div class="display-option">
            <div class="refine">
              <div class="sorting">
                <button class="menu_trigger">
                  <span class="option_selected">{{
                    translate('SEARCH_RESULTS_SORT_RECOMMENDED')
                  }}</span>
                </button>
                <ul class="sorting_panel">
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_RECOMMENDED') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_NEWEST') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_TIME_REMAINING_ASC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_TIME_REMAINING_DESC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_PRICE_ASC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_PRICE_DESC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_BID_COUNT_DESC') }}</a>
                  </li>
                  <li class="option_item">
                    <a>{{ translate('SEARCH_RESULTS_SORT_BID_COUNT_ASC') }}</a>
                  </li>
                </ul>
              </div>
              <div class="check-onsale">
                <div class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" /><label
                    for="checkbox"
                    >{{ translate('SEARCH_RESULTS_ON_SALE_ONLY') }}</label
                  >
                </div>
              </div>
            </div>
            <div class="switch">
              <p class="dl">
                <a href="./" @click.prevent="handleCsvDownload($event)"
                  ><span>{{ translate('SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_INFO') }}</span></a
                >
              </p>
              <div class="number-switch">
                <p class="label">{{ translate('SEARCH_RESULTS_DISPLAY_COUNT') }}</p>
                <div class="num">
                  <button class="btn" :class="{'is-active': itemsPerPage == 20}" @click="setItemsPerPage(20)">{{ translate('SEARCH_RESULTS_20_ITEMS') }}</button>
                  <button class="btn" :class="{'is-active': itemsPerPage == 50}" @click="setItemsPerPage(50)">{{ translate('SEARCH_RESULTS_50_ITEMS') }}</button>
                  <button class="btn" :class="{'is-active': itemsPerPage == 100}" @click="setItemsPerPage(100)">{{ translate('SEARCH_RESULTS_100_ITEMS') }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 商品一覧（入札欄あり　Biddable Items） -->
          <RowBidAuctionItem
            v-for="(item, index) in biddableItems"
            :key="String(item.item_no) + String(item.lot_id)"
            :item="item"
            @refresh="refreshList"
          />
        </template>

        <!-- Pagination -->
        <div v-if="totalPages >= 1" class="wrap-btn pagination">
          <!-- <p>{{ filteredTotalCount }}件中 {{ currentItemsStart }}〜{{ currentItemsEnd }}件を表示</p> -->
          <p>
            {{ totalCount }}{{ translate('SEARCH_RESULTS_PAGINATION_OF') }}
            {{ currentItemsStart }}〜{{ currentItemsEnd
            }}{{ translate('SEARCH_RESULTS_PAGINATION_DISPLAYING') }}
          </p>
          <nav class="pagination">
            <ul>
              <!-- Previous button -->
              <li class="prev">
                <a
                  href="#"
                  :class="{disabled: !canGoPrevious}"
                  @click.prevent="goToPreviousPage"
                ></a>
              </li>

              <!-- Page numbers -->
              <li v-for="page in pageNumbers" :key="page">
                <template v-if="page === -1">
                  <!-- Ellipsis -->
                  <span class="ellipsis">...</span>
                </template>
                <template v-else>
                  <a
                    href="#"
                    :class="{active: page === currentPage}"
                    @click.prevent="goToPage(page)"
                  >
                    {{ page }}
                  </a>
                </template>
              </li>

              <!-- Next button -->
              <li class="next">
                <a href="#" :class="{disabled: !canGoNext}" @click.prevent="goToNextPage"></a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </section>

    <!-- BidConfirmModal -->
    <BidConfirmModal
      v-if="true"
      v-model="bid.showBidConfirm"
      @refresh="refreshList"
      :isAscendingAuction="isAscendingAuction"
    />
  </main>
</template>

<style scoped>
  /* Dynamic pagination styling */
  .pagination .ellipsis {
    padding: 8px 12px;
    color: #666;
    cursor: default;
  }

  .pagination a.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .pagination a.active {
    background-color: #007bff;
    color: white;
    border-radius: 4px;
  }

  .pagination a:hover:not(.disabled):not(.active) {
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .auction-type-label .type-tab-wrap span.active {
    font-weight: bold;
    color: #007bff;
    border-bottom: 2px solid #007bff;
  }

  .auction-type-label .type-tab-wrap a {
    cursor: pointer;
    color: #666;
    text-decoration: none;
  }

  .auction-type-label .type-tab-wrap a:hover {
    color: #007bff;
  }
</style>
