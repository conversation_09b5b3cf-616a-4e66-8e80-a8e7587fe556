<script setup>
  import useSearchProducts from '@/composables/searchProducts'
import {CLASSIFICATIONS, PATH_NAME} from '@/defined/const'
import {useCognitoAuthStore} from '@/stores/cognitoAuth.ts'
import useChangeLanguage, {useLanguageStore} from '@/stores/language'
import {useSearchResultStore} from '@/stores/search-results'
import {useTenantSettingsStore} from '@/stores/tenantSettings'
import $ from 'jquery'
import {computed, onMounted, ref, watch} from 'vue'
import {RouterLink, useRoute, useRouter} from 'vue-router'
import {useLocale} from 'vuetify'

  defineProps({
    localizedStaticPages: {
      type: Array,
      default: () => []
    }
  })

  const auth = useCognitoAuthStore()
  const tenantSettings = useTenantSettingsStore()
  const store = useSearchResultStore()
  const router = useRouter()
  const route = useRoute()
  const keyword = ref('')
  const languageStore = useLanguageStore()
  const {changeLanguage} = useChangeLanguage()
  const {t: translate, current: locale} = useLocale()
  const isChangingLanguage = ref(false)

  const {getCategoryTreeList, categories} = useSearchProducts()

  const logoText = computed(() => {
    return tenantSettings.companyName || tenantSettings.tenantName
  })

  const hasStaticPage = pagePath => {
    return tenantSettings.getStaticPage(pagePath) !== null
  }

  const handleLogout = async () => {
    console.log('Logging out user')
    try {
      await auth.logout()
    } catch (error) {
      console.error('Logout error:', error)
      window.location.href = PATH_NAME.LOGIN
    }
  }

  const handleLanguageChange = async event => {
    const selectedLanguage = event.target.value
    if (selectedLanguage === languageStore.currentLanguage) return

    isChangingLanguage.value = true
    try {
      locale.value = selectedLanguage
      // Update store and API
      await changeLanguage(selectedLanguage)
    } catch (error) {
      // Rollback on error
      locale.value = languageStore.currentLanguage
      console.error('Failed to change language:', error)
    } finally {
      isChangingLanguage.value = false
    }
  }

  const handleSearch = () => {
    if (!keyword.value) return
    router.push({path: PATH_NAME.SEARCH_RESULTS, query: {keyword: keyword.value}})
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
  }

  onMounted(async () => {
    try {
      await getCategoryTreeList()
    } catch (error) {
      console.error('Failed to fetch categories:', error)
    }
    // Toggle navigation menu on mobile
    $('header p.btnMenu').on('click', function () {
      $('header .gNav').slideToggle()
      $(this).toggleClass('close')
    })

    // SP Nav child open/close in Header
    $('header .gNav nav ul.only_sp li p').on('click', function () {
      $(this).next('ul').slideToggle()
      $(this).toggleClass('close')
    })
    // Hide the gNav after clicking the most child element
    $('header .gNav nav ul.only_sp li ul li').on('click', () => {
      $('header .gNav').slideUp()
      $('.btnMenu').removeClass('close')
    })

    // SP Nav child open/close in Footer
    $('footer nav .fNav_sp > ul > li > p').on('click', function () {
      $(this).next('ul,dl').slideToggle()
      $(this).toggleClass('close')
    })
  })

  watch(
    () => route.path,
    () => {
      // SlideUP the gNav after path changed
      $('header .gNav').slideUp()
      $('.btnMenu').removeClass('close')
    }
  )

  // Handle category click - navigate to search results with category filter
  const handleCategoryClick = category => {
    router.push({
      path: PATH_NAME.SEARCH_RESULTS,
      query: {keyword: keyword.value, category: [String(category.category_tree_id)]},
    })
    // SP: Slide up menu after navigation
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
  }

  // "すべてのカテゴリー" click
  const handleAllCategoriesClick = () => {
    router.push({
      path: PATH_NAME.SEARCH_RESULTS,
      query: {keyword: keyword.value},
    })
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
  }

  const handleAuctionTypeClick = auctionType => {
    router.push({
      path: PATH_NAME.SEARCH_RESULTS,
      query: {keyword: keyword.value},
    })
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
    const classificationValue =
      auctionType === 'ascending' ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
    store.selectedAucClassification = classificationValue
  }
</script>

<template>
  <!-- Header -->
  <header>
    <!-- gNav PC/SP start -->
    <div class="wrap-header-elm">
      <div class="l-header-info-links">
        <ul class="l-header-info-item">
          <li class="language-swich">
            <div class="lang-wrap">
              <select
                id="locale-switcher"
                class="lang"
                :value="languageStore.currentLanguage"
                @change="handleLanguageChange"
                :disabled="isChangingLanguage"
              >
                <option value="ja" :selected="languageStore.currentLanguage === 'ja'">JP</option>
                <option value="en" :selected="languageStore.currentLanguage === 'en'">EN</option>
              </select>
            </div>
          </li>
        </ul>
      </div>
      <div class="main-nav-wrap">
        <div class="h-top">
          <p class="btnMenu only_sp"><span class="ham"></span></p>
          <h1 class="h-top-logo">
            <RouterLink class="logo" :to="PATH_NAME.TOP">
              <img src="@/assets/img/common/logo_cecauction.png" alt="CEC AUCTION" />
            </RouterLink>
          </h1>

        <div class="h-top-menu only_sp">
          <div class="account">
            <RouterLink :to="PATH_NAME.MYPAGE_EDIT" class="account-link">
              <img src="@/assets/img/common/icn_nav_member.svg" alt="Account" />
            </RouterLink>
          </div>
        </div>
        </div>
        <div class="nav-elm">
          <div class="search-elm only_pc">
            <div class="search-category">
              <li>
                <a href="#" class="nav-label">{{ translate('HEADER_NAV_PRODUCT_CATEGORY') }}</a>
                <div class="menu-list">
                  <p class="arrow-box"></p>

                  <div class="panel-wrap">
                    <div class="category-box">
                      <div class="category-all">
                        <p>
                          <a href="#" @click.prevent="handleAuctionTypeClick('ascending')">{{
                            translate('ASCENDING_AUCTION')
                          }}</a>
                        </p>
                        <p>
                          <a href="#" @click.prevent="handleAuctionTypeClick('sealed')">{{
                            translate('SEALED_AUCTION')
                          }}</a>
                        </p>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a href="#" @click.prevent="handleAllCategoriesClick()"
                            >{{ translate('HEADER_NAV_ALL_CATEGORIES') }}
                          </a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li v-for="category in categories" :key="category.category_tree_id">
                            <a href="#" @click.prevent="handleCategoryClick(category)">
                              {{ category.category_tree_name }}
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </div>
            <div class="search-keyword">
              <input
                v-model="keyword"
                type="text"
                data-id="shop-search-keyword"
                value=""
                class="side-search-keyword search-keyword"
                :placeholder="translate('HEADER_NAV_SEARCH_PLACEHOLDER')"
              />
              <button @click="handleSearch">
                <img src="@/assets/img/common/icn_search_gray.svg" />
              </button>
            </div>
            <div class="info-menu">
              <li>
                <a href="#" class="nav-label">{{ translate('HEADER_NAV_SITE_ABOUT') }}</a>
                <div class="menu-list">
                  <p class="arrow-box"></p>
                  <ul>
                    <li v-for="page in localizedStaticPages" :key="page.static_page_no">
                      <RouterLink :to="`/${page.page_path}`">
                        {{ page.page_name }}
                      </RouterLink>
                    </li>
                  </ul>
                </div>
              </li>
            </div>
          </div>
          <ul class="nav-btn only_pc">
            <li class="nav-mypage favorite" v-if="!auth.isAuthenticated">
              <RouterLink :to="PATH_NAME.FAVORITES">
                <img src="@/assets/img/common/icn_nav_member.svg" />
                <span>{{ translate('HEADER_NAV_LOGIN') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage favorite">
              <RouterLink :to="PATH_NAME.FAVORITES">
                <img src="@/assets/img/common/icn_header_favorite.svg" />
                <span>{{ translate('HEADER_NAV_FAVORITES') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage bid">
              <RouterLink :to="PATH_NAME.BIDS">
                <img src="@/assets/img/common/icn_bid.svg" class="bid" />
                <span>{{ translate('HEADER_NAV_BIDDING') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage bidded">
              <RouterLink :to="PATH_NAME.BID_HISTORY">
                <img src="@/assets/img/common/icn_bidded.svg" class="bidded" />
                <span>{{ translate('HEADER_NAV_SUCCESSFUL_BID_HISTORY') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage registration" v-if="!auth.isAuthenticated">
              <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
                <img src="@/assets/img/common/icn_registration.svg" class="bidded" />
                <span>{{ translate('HEADER_NAV_REGISTER') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage favorite" v-if="auth.isAuthenticated">
              <a @click="handleLogout" class="auth-button">
                <img src="@/assets/img/common/icn_nav_logout.svg" />
                <span>{{ translate('HEADER_NAV_LOGOUT') }}</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- gNav PC/SP end -->
    <!-- gNav SP start -->
    <div class="gNav only_sp">
      <nav>
        <ul class="only_sp">
          <li class="language-swich">
            <div class="lang-wrap">
              <select
                id="locale-switcher"
                class="lang"
                :value="languageStore.currentLanguage"
                @change="handleLanguageChange"
                :disabled="isChangingLanguage"
              >
                <option value="ja" :selected="languageStore.currentLanguage === 'ja'">JP</option>
                <option value="en" :selected="languageStore.currentLanguage === 'en'">EN</option>
              </select>
            </div>
          </li>
          <li class="account">
            <button
              class="btn entry"
              @click="router.push(PATH_NAME.ENTRY_INFO_REGIST)"
              v-if="!auth.isAuthenticated"
            >
              {{ translate('HEADER_NAV_NEW_MEMBER_REGISTER') }}
            </button>
            <button
              class="btn login"
              @click="router.push(PATH_NAME.FAVORITES)"
              v-if="!auth.isAuthenticated"
            >
              {{ translate('HEADER_NAV_LOGIN') }}
            </button>
            <button class="btn login" @click="handleLogout" v-if="auth.isAuthenticated">
              {{ translate('HEADER_NAV_LOGOUT') }}
            </button>
          </li>
          <li class="search">
            <input
              v-model="keyword"
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="search-keyword"
              :placeholder="translate('HEADER_NAV_SEARCH_PLACEHOLDER')"
              @keyup.enter="handleSearch"
            />
            <button @click="handleSearch"><img src="@/assets/img/common/icn_search.svg" /></button>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_PRODUCT_CATEGORY') }}</p>
            <ul>
              <li>
                <a href="#" @click.prevent="handleAuctionTypeClick('ascending')">{{
                  translate('ASCENDING_AUCTION')
                }}</a>
              </li>
              <li>
                <a href="#" @click.prevent="handleAuctionTypeClick('sealed')">{{
                  translate('SEALED_AUCTION')
                }}</a>
              </li>
              <li>
                <a href="#" @click.prevent="handleAllCategoriesClick()">{{
                  translate('HEADER_NAV_ALL_CATEGORIES')
                }}</a>
              </li>
              <li v-for="category in categories" :key="category.category_tree_id">
                <a href="#" @click.prevent="handleCategoryClick(category)">
                  {{ category.category_tree_name }}
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_MEMBER_MENU') }}</p>
            <ul>
              <li v-if="!auth.isAuthenticated">
                <RouterLink :to="PATH_NAME.FAVORITES">{{
                  translate('HEADER_NAV_LOGIN')
                }}</RouterLink>
              </li>
              <li v-if="auth.isAuthenticated">
                <a href="#" @click.prevent="handleLogout">{{ translate('HEADER_NAV_LOGOUT') }}</a>
              </li>
              <li class="nav-mypage registration" v-if="auth.isAuthenticated">
                <RouterLink :to="PATH_NAME.MYPAGE_EDIT">{{ translate('HEADER_NAV_MY_PAGE') }}</RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_SITE_ABOUT') }}</p>
            <ul>
              <li v-if="hasStaticPage('service')">
                <RouterLink :to="PATH_NAME.SERVICE">{{
                  translate('HEADER_NAV_SERVICE')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('membership')">
                <RouterLink :to="PATH_NAME.MEMBERSHIP">{{
                  translate('HEADER_NAV_MEMBERSHIP')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('faq')">
                <RouterLink :to="PATH_NAME.FAQ">{{ translate('HEADER_NAV_FAQ') }}</RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_GUIDANCE') }}</p>
            <ul>
              <li v-if="hasStaticPage('profile')">
                <RouterLink :to="PATH_NAME.PROFILE">{{
                  translate('HEADER_NAV_COMPANY_INFO')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('service')">
                <RouterLink :to="PATH_NAME.SERVICE">{{
                  translate('HEADER_NAV_SERVICE')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('membership')">
                <RouterLink :to="PATH_NAME.MEMBERSHIP">{{
                  translate('HEADER_NAV_MEMBERSHIP')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('faq')">
                <RouterLink :to="PATH_NAME.FAQ">{{ translate('HEADER_NAV_FAQ') }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('terms')">
                <RouterLink :to="PATH_NAME.TERMS">{{
                  translate('HEADER_NAV_TERMS_OF_SERVICE')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('privacy')">
                <RouterLink :to="PATH_NAME.PRIVACY">{{
                  translate('HEADER_NAV_PRIVACY_POLICY')
                }}</RouterLink>
              </li>
              <li v-if="hasStaticPage('tokusho')">
                <RouterLink :to="PATH_NAME.TOKUSHO">{{
                  translate('HEADER_NAV_TOKUSHO')
                }}</RouterLink>
              </li>
            </ul>
          </li>
        </ul>
        <div class="line-logo">
          <div class="cont-wrap">
            <div class="pct">
              <RouterLink :to="PATH_NAME.TOP">
                <img src="@/assets/img/common/logo_cecauction.svg" />
              </RouterLink>
            </div>
            <div class="sns">
              <ul>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_facebook.svg" class="facebook"
                  /></a>
                </li>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_x.svg" class="x" /></a>
                </li>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_instagram.svg" class="instagram"
                  /></a>
                </li>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_youtube.svg" class="youtube"
                  /></a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="line-copyright">
          <div class="cont-wrap">
            <ul>
              <li>
                <a href="./">{{ translate('HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW') }}</a>
              </li>
              <li>
                <a href="./">{{ translate('HEADER_NAV_PRIVACY_POLICY') }}</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
    <!-- gNav SP end -->
  </header>
</template>

<style lang="css" scoped>
  .nav-elm {
    align-items: center;
  }
  .info-menu {
    width: 120px !important;
  }

  .auth-button {
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .auth-button:hover {
    opacity: 0.8;
  }

  button.auth-button {
    background: none;
    border: none;
    font: inherit;
    color: inherit;
    text-decoration: none;
  }

  a.auth-button {
    text-decoration: none;
  }
</style>
