<script setup lang="ts">
  import type {AuctionClassification} from '@/composables/_type'
  import useFavorite from '@/composables/favorite'
  import useSearchProducts from '@/composables/searchProducts'
  import useApi from '@/composables/useApi'
  import {useLanguageRefetch} from '@/composables/useLanguageRefetch.ts'
  import {CLASSIFICATIONS, PATH_NAME} from '@/defined/const'
  import {useTypedI18n} from '@/language/index.ts'
  import router from '@/router'
  import {useLanguageStore} from '@/stores/language.js'
  import {usePrevRouteStore} from '@/stores/prev-route.js'
  import {useSearchResultStore} from '@/stores/search-results.js'
  import {computed, defineAsyncComponent, onMounted, onUnmounted, ref} from 'vue'
  import {RouterLink} from 'vue-router'
  import {useSlickSlider} from './useSlickSlider.js'

  const {apiExecute} = useApi()
  const {refetchOnLanguageChange} = useLanguageRefetch()

  const PanelAuctionItem = defineAsyncComponent(
    () => import('@/components/common/auction/PanelAuctionItem.vue')
  )

  const store = useSearchResultStore()
  const {initSlider, cleanupSlider} = useSlickSlider()
  const {toggleFavorite} = useFavorite()
  const languageStore = useLanguageStore()
  const {t: translate} = useTypedI18n()

  const notices = ref([])

  // 最新3件を格納する変数
  const latestNotices = computed(() => {
    return [...notices.value]
      .sort((a, b) => new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime())
      .slice(0, 3)
  })

  // display_codeが重要で、start_datetimeが最新のお知らせを1件取得
  const latestImportantNotice = computed(() => {
    return [...notices.value]
      .filter(n => n.display_code === '3')
      .sort((a, b) => new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime())
  })
  const {categories, getCategoryTreeList, search} = useSearchProducts()
  const newItems = ref([])
  const keyword = ref('')
  const showMore = ref(false)
  const selectedCategory = ref('')
  const selectedCategoryName = ref('')

  const recommendedItems = computed(() => {
    return store.productList.all.filter(item => item.is_recommending === true)
  })

  // TODO
  const newItemsFiltered = computed(() => {
    return store.productList.all
  })

  const {goToPath} = usePrevRouteStore()

  const refetchNoticeList = async () => {
    // 言語切り替えに対応するため、商品詳細情報を再取得
    await getNoticeList()
  }

  const getNoticeList = async () => {
    const response = await apiExecute('public/get-notice-list-by-auction', {})
    notices.value = response
    console.log('notices', notices.value)
  }

  // Event handlers
  const handleFavoriteToggle = async (exhibitionItemNo: string, currentFavorited: boolean) => {
    console.log('handleFavoriteToggle', exhibitionItemNo, currentFavorited)
    await toggleFavorite(exhibitionItemNo, currentFavorited)
  }

  const handleClickItem = item => {
    console.log('Item clicked:', item)
    router.push(PATH_NAME.DETAILS)
  }

  const handleRefresh = async () => {
    console.log('Refresh handler called')
    await search({})
  }

  /**
   * Handle search button click
   * Navigate to search results with keyword and selected category
   */
  const handleSearch = () => {
    const query: any = {
      keyword: keyword.value ? keyword.value : undefined,
    }

    // Add category to query if one is selected
    if (selectedCategory.value) {
      query.category = [String(selectedCategory.value)]
    }

    router.push({
      path: PATH_NAME.SEARCH_RESULTS,
      query: query,
    })
  }

  const handleCategoryClick = (category: any) => {
    router.push({
      path: PATH_NAME.SEARCH_RESULTS,
      query: {keyword: keyword.value, category: [String(category.category_tree_id)]},
    })
  }

  const handleAuctionTypeClick = (classificationType: AuctionClassification) => {
    const classificationValue =
      classificationType === 'ascending' ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
    store.selectedAucClassification = classificationValue
  }

  const handleMoreCategory = () => {
    console.log('More category clicked')
    router.push(PATH_NAME.CATEGORY_LIST)
  }

  /**
   * Handle category selection from dropdown
   * Only stores the selected category without performing search
   * @param {Object} category - Selected category object
   */
  const handleCategorySelect = category => {
    selectedCategory.value = category.category_tree_id
    selectedCategoryName.value = category.category_tree_name
    showMore.value = false
  }

  /**
   * Handle "All Categories" selection
   * Only resets the selected category without performing search
   */
  const handleAllCategoriesSelect = () => {
    selectedCategory.value = ''
    selectedCategoryName.value = translate('TOP_SEARCH_ALL_CATEGORIES')
    showMore.value = false
  }

  /**
   * Toggle category dropdown visibility
   */
  const toggleCategoryDropdown = () => {
    showMore.value = !showMore.value
  }

  // Handlers for View Only Auction Item
  const handlers = {
    onFavoriteToggle: handleFavoriteToggle,
    onRefresh: handleRefresh,
    onItemClick: handleClickItem,
  }

  onMounted(async () => {
    refetchOnLanguageChange(refetchNoticeList)

    getCategoryTreeList()
    await initSlider('.list-item-gallery')
    await getNoticeList()

    // Initialize selected category name
    selectedCategoryName.value = translate('TOP_SEARCH_ALL_CATEGORIES')
  })

  onUnmounted(() => {
    cleanupSlider('.list-item-gallery')
  })
</script>

<template>
  <main id="main">
    <section id="alert-info" v-if="latestImportantNotice.length > 0">
      <ul class="info-item">
        <li v-for="notice in latestImportantNotice" :key="notice.notice_no">
          <!-- hrefの中を変える -->
          <a :href="`${PATH_NAME.NOTICE_LIST}/${notice.notice_no}`" class="link-wrap">
            <span class="notice-day">
              {{ notice.start_datetime }}
            </span>
            <span class="notice-ttl">
              {{ notice.title }}
            </span>
          </a>
        </li>
      </ul>
    </section>
    <section id="heroes">
      <p class="catch">Auction Coming Soon!</p>
    </section>
    <section class="nav-list-mode-wrap">
      <div class="container">
        <RouterLink :to="PATH_NAME.SEARCH_RESULTS" @click="handleAuctionTypeClick('ascending')">
          <span>{{ translate('ASCENDING_AUCTION') }}</span>
        </RouterLink>
        <RouterLink :to="PATH_NAME.SEARCH_RESULTS" @click="handleAuctionTypeClick('sealed')">
          <span>{{ translate('SEALED_AUCTION') }}</span>
        </RouterLink>
      </div>
    </section>
    <section id="search">
      <div class="cont-wrap">
        <div class="search-keyword">
          <input
            type="text"
            v-model="keyword"
            data-id="shop-search-keyword"
            class="side-search-keyword"
            :placeholder="translate('FILTER_BOX_SEARCH_PLACEHOLDER')"
          />
          <!-- カテゴリボタン start -->
          <div class="btn-category">
            <a href="javascript:void(0)" @click="toggleCategoryDropdown"
              ><span>{{ selectedCategoryName || translate('TOP_SEARCH_ALL_CATEGORIES') }}</span></a
            >
          </div>
          <div class="filter-panel" :class="{'is-active': showMore}" v-show="showMore">
            <div class="panel-body">
              <div class="list-wrap">
                <ul class="list">
                  <!-- All Categories option -->
                  <li
                    class="label"
                    :class="{active: !selectedCategory}"
                    @click="handleAllCategoriesSelect"
                  >
                    {{ translate('TOP_SEARCH_ALL_CATEGORIES') }}
                  </li>
                  <!-- Individual category options -->
                  <template
                    v-for="(category, index) in categories"
                    :key="category.category_tree_id"
                  >
                    <li
                      class="label"
                      :class="{active: selectedCategory === category.category_tree_id}"
                      @click="handleCategorySelect(category)"
                    >
                      {{ category.category_tree_name }}
                    </li>
                  </template>
                </ul>
              </div>
              <p @click="showMore = false" class="close-filter">
                <span> </span>
              </p>
            </div>
          </div>
          <!-- カテゴリボタン end -->
        </div>
        <button @click="handleSearch" class="btn-search">
          <img src="@/assets/img/common/icn_search.svg" />
        </button>
      </div>
    </section>

    <!-- カテゴリーから探す Start -->
    <section class="search-category">
      <p class="ttl">{{ translate('TOP_SEARCH_BY_CATEGORY') }}</p>
      <ul class="list-category">
        <li v-for="category in categories" :key="category.id">
          <a
            @click.prevent="handleCategoryClick(category)"
            :href="category.routePath"
            :title="category.description"
          >
            <figure>
              <img src="@/assets/img/category/top_category01.png" :alt="category.name" />
            </figure>
            <p>{{ category.category_tree_name }}</p>
          </a>
        </li>
      </ul>
      <button class="btn more-category" @click="handleMoreCategory">
        <span>{{ translate('COMMON_MORE') }}</span>
      </button>
    </section>
    <!-- カテゴリーから探す End -->
    <!-- Information Start -->
    <section id="info">
      <div class="container-grid">
        <h2>
          NEWS<span>{{ translate('TOP_UPDATE_INFO') }}</span>
        </h2>
        <ul class="info-item" v-if="latestNotices.length">
          <li v-for="notice in latestNotices" :key="notice.notice_no">
            <RouterLink :to="`${PATH_NAME.NOTICE_LIST}/${notice.notice_no}`">
              <span class="notice-day">{{ notice.start_datetime }}</span>
              <span class="notice-title">{{ notice.title }}</span>
            </RouterLink>
          </li>
        </ul>
        <div v-else class="info-item">
          <li>{{ translate('NOTICE_EMPTY') }}</li>
        </div>
        <div class="more-btn-wrap">
          <RouterLink :to="PATH_NAME.NOTICE_LIST" class="btn more">{{
            translate('COMMON_MORE')
          }}</RouterLink>
        </div>
      </div>
    </section>
    <!-- Information End -->

    <!-- おすすめ Start -->
    <section id="list-recommend" class="list-item list-slider">
      <h2>
        <p class="ttl">{{ translate('TOP_RECOMMEND_PRODUCT') }}</p>
      </h2>
      <div class="container">
        <div v-if="recommendedItems.length > 0" :class="['item-list', 'panel']">
          <ul>
            <PanelAuctionItem
              v-for="item in recommendedItems"
              :key="item.exhibition_item_no"
              :item="item"
              view-mode="panel"
              :handlers="handlers"
              custom-classes=""
            />
          </ul>
        </div>
      </div>
    </section>
    <!-- おすすめ End -->

    <!-- Item List Start -->
    <section id="list-new" class="list-item">
      <h2>
        <p class="ttl">{{ translate('TOP_NEW_PRODUCTS') }}</p>
        <RouterLink :to="PATH_NAME.SEARCH_RESULTS" class="btn more">{{
          translate('COMMON_MORE')
        }}</RouterLink>
      </h2>

      <div class="container">
        <div v-if="newItemsFiltered.length > 0" :class="['item-list', 'panel']">
          <ul>
            <PanelAuctionItem
              v-for="item in newItemsFiltered"
              :key="item.exhibition_item_no"
              :item="item"
              view-mode="panel"
              :handlers="handlers"
              custom-classes=""
            />
          </ul>
        </div>
      </div>
    </section>
    <!-- Item List End -->
    <!-- about Start -->
    <section id="about">
      <div class="cont-wrap">
        <h2>
          <p class="ttl">{{ translate('TOP_ABOUT_TITLE') }}</p>
          <p class="s-ttl">{{ translate('TOP_ABOUT_SUBTITLE') }}</p>
        </h2>
        <div class="read">
          <p class="sb">
            {{ translate('TOP_ABOUT_DESCRIPTION_1') }}
          </p>
          <p class="st">
            {{ translate('TOP_ABOUT_DESCRIPTION_2') }}
          </p>
          <p class="sb">{{ translate('TOP_ABOUT_DESCRIPTION_3') }}</p>
          <p class="st">
            {{ translate('TOP_ABOUT_DESCRIPTION_4') }}
          </p>
        </div>
        <div class="btn-wrap">
          <a href="./other/about/" class="btn more">{{ translate('COMMON_MORE') }}</a>
        </div>
      </div>
    </section>
    <!-- about End -->
    <!-- signup Start -->
    <section id="signup">
      <div class="cont-wrap">
        <h2>
          <p class="s-ttl">{{ translate('TOP_SIGNUP_SUBTITLE') }}</p>
          <p class="ttl">{{ translate('TOP_SIGNUP_TITLE') }}</p>
        </h2>
        <div class="read">
          <p class="sb">{{ translate('TOP_SIGNUP_FREE_SHIPPING_TITLE') }}</p>
          <p class="st">
            {{ translate('TOP_SIGNUP_FREE_SHIPPING_DESC')
            }}<a href="/">{{ translate('TOP_SIGNUP_FREE_SHIPPING_LINK') }}</a
            >{{ translate('TOP_SIGNUP_FREE_SHIPPING_SUFFIX') }}
          </p>
          <p class="sb">{{ translate('TOP_SIGNUP_COUPON_TITLE') }}</p>
          <p class="st">
            {{ translate('TOP_SIGNUP_COUPON_DESC')
            }}<a href="./signin">{{ translate('TOP_SIGNUP_COUPON_LOGIN_LINK') }}</a
            >{{ translate('TOP_SIGNUP_COUPON_SUFFIX') }}
          </p>
        </div>
        <div class="btn-wrap">
          <a href="./other/member/" class="btn more">{{
            translate('TOP_SIGNUP_MEMBER_MORE_BUTTON')
          }}</a>
          <a href="./other/member/" class="btn signup">{{
            translate('TOP_SIGNUP_REGISTER_BUTTON')
          }}</a>
        </div>
      </div>
    </section>
    <!-- signup End -->
  </main>
</template>
