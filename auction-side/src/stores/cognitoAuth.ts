import {
  fetchAuthSession as amplifyFetchAuthSession,
  confirmResetPassword,
  confirmSignIn,
  signIn,
  signOut,
} from '@aws-amplify/auth'
import { Hub } from 'aws-amplify/utils'
import { jwtDecode } from 'jwt-decode'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import useApi from '../composables/useApi'
import i18n from '../language/index'
import type { TranslationKey } from '../language/translate'
import { useLanguageStore } from './language'

// Function for use in Pinia stores
const t = (key: TranslationKey): string => {
  return i18n.global.t(key as string) as string
}

const suppressRedirect = ref(false) // リダイレクト抑制フラグ

interface LoginHistoryMemberInfo {
  member_no: number
  user_no: number
  user_id: string
  member_id: string
  free_field: {
    email: string
    language: string
    memberName: string
  }
}

interface LoginHistoryResponse {
  success: boolean
  memberInfo: LoginHistoryMemberInfo
  message: string
}

interface CognitoUser {
  email: string
  memberNo: string
  userNo: string
  memberName: string
  tenantId: string
}

interface LoginResult {
  type: 'SUCCESS' | 'NEW_PASSWORD_REQUIRED' | 'EMAIL_VERIFICATION_REQUIRED' | 'NEED_OTP' | 'ERROR'
  message?: string
}

interface LoginHistoryResult {
  success: boolean
  error?: string
}

interface LoginMemberStatusResponse {
  status: number
  error?: string
}

// Translates Cognito error objects into localized messages
const errorToMessage = (error: any): string => {
  if (!error) return t('LOGIN_ERROR_UNKNOWN')

  if (error.message && error.message.includes('User is disabled.')) {
    return t('LOGIN_ERROR_INVALID_CREDENTIALS')
  }

  switch (error.name) {
    case 'UserNotConfirmedException':
      return t('LOGIN_ERROR_INVALID_CREDENTIALS')
    case 'UserNotFoundException':
      return t('LOGIN_ERROR_INVALID_CREDENTIALS')
    case 'NotAuthorizedException':
      return t('LOGIN_ERROR_INVALID_CREDENTIALS')
    case 'UserAlreadyAuthenticatedException':
      return t('LOGIN_ERROR_USER_ALREADY_AUTHENTICATED')
    case 'LimitExceededException':
      return t('LOGIN_ERROR_LIMIT_EXCEEDED')
    case 'InvalidPasswordException':
      return t('LOGIN_ERROR_INVALID_PASSWORD')
    case 'TooManyRequestsException':
      return t('LOGIN_ERROR_TOO_MANY_REQUESTS')
    default:
      return error.message || t('LOGIN_ERROR_LOGIN_FAILED')
  }
}

// Translates OTP-specific Cognito error objects into localized messages
const otpErrorToMessage = (error: any): string => {
  console.log('error: ', error)
  if (!error) return t('LOGIN_ERROR_UNKNOWN')

  if (error.message && error.message.includes('Invalid session for the user')) {
    return t('LOGIN_ERROR_OTP_SESSION_EXPIRED')
  }
  if (error.message && error.message.includes('An error occurred during the sign in process.')) {
    return t('LOGIN_ERROR_OTP_SESSION_EXPIRED')
  }
  if (error.message && error.message.includes('Invalid verification code')) {
    return t('LOGIN_ERROR_INVALID_OTP_CODE')
  }
  if (error.message && error.message.includes('Incorrect username or password')) {
    return t('LOGIN_ERROR_INVALID_OTP_CODE')
  }
  switch (error.name) {
    case 'UserNotConfirmedException':
      return t('LOGIN_ERROR_USER_NOT_CONFIRMED')
    case 'UserNotFoundException':
      return t('LOGIN_ERROR_INVALID_CREDENTIALS')
    case 'UserAlreadyAuthenticatedException':
      return t('LOGIN_ERROR_USER_ALREADY_AUTHENTICATED')
    case 'LimitExceededException':
      return t('LOGIN_ERROR_LIMIT_EXCEEDED')
    case 'InvalidPasswordException':
      return t('LOGIN_ERROR_INVALID_PASSWORD')
    case 'TooManyRequestsException':
      return t('LOGIN_ERROR_TOO_MANY_REQUESTS')
    default:
      return error.message || t('LOGIN_ERROR_INVALID_OTP_CODE')
  }
}

// Extended JWT payload interface for Cognito tokens
interface CognitoJwtPayload {
  email: string
  'custom:member_no': string
  'custom:user_no': string
  'custom:member_name': string
  'tenant-id': string
  [key: string]: any
}

export const useCognitoAuthStore = defineStore('cognitoAuth', () => {
  const router = useRouter()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const languageStore = useLanguageStore()

  const user = ref<CognitoUser | null>(null)
  const idToken = ref<string | null>(null)
  const accessToken = ref<string | null>(null)
  const memberInfo = ref<LoginHistoryMemberInfo | null>(null)
  const isAuthenticated = computed(() => !!user.value && !!idToken.value)

  /**
   * Sets the user information from the JWT token.
   * @param token
   */
  const _setUserFromToken = (token: string | null) => {
    if (!token) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      return
    }

    const decodedToken = jwtDecode<CognitoJwtPayload>(token)
    user.value = {
      email: decodedToken.email,
      memberNo: decodedToken['custom:member_no'],
      userNo: decodedToken['custom:user_no'],
      memberName: decodedToken['custom:member_name'],
      tenantId: decodedToken['cognito:groups'][0].split(':')[1], // number, exg. 1
    }
    idToken.value = token
  }

  /**
   *
   * @param forceRefresh
   */
  const fetchAuthSession = async (forceRefresh: boolean = false): Promise<boolean> => {
    try {
      const session = await amplifyFetchAuthSession({forceRefresh})
      const token = session.tokens?.idToken?.toString()
      if (!token) {
        throw new Error('No token found in session')
      }
      _setUserFromToken(token)
      accessToken.value = session.tokens?.accessToken?.toString() || null
      return true
    } catch (e) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      memberInfo.value = null
      return false
    }
  }

  /**
   *
   */
  const logLoginHistory = async (): Promise<LoginHistoryResult> => {
    try {
      const result = await apiExecute<LoginHistoryResponse>(
        'private/login-history-logging',
        {
          languageCode: 'ja',
        },
        false, // not a file upload
        {
          authorization: `Bearer ${idToken.value}`,
        }
      )
      console.log('Login history logged successfully')

      // Store the memberInfo from the response
      if (result.success && result.memberInfo) {
        memberInfo.value = result.memberInfo
      }

      return {success: true}
    } catch (error: any) {
      console.warn('Login history logging error:', error)

      const errorMessage = error?.response?.data?.message || t('LOGIN_ERROR_LOGIN_HISTORY_FAILED')
      return {success: false, error: errorMessage}
    }
  }

  const logLoginMemberStatus = async (): Promise<LoginMemberStatusResponse> => {
    try {
      const result = await apiExecute<LoginMemberStatusResponse>('private/get-member-status', {})
      return {status: result.status}
    } catch (error: any) {
      const errorMessage = parseHtmlResponseError(error)
      console.warn('Get Member Status error:', errorMessage)
      const message = errorMessage?.message || t('LOGIN_ERROR_MEMBER_STATUS_FAILED')
      return {status: null, error: message}
    }
  }

  /**
   * Logs in the user with email and password.
   * @param email
   * @param password
   */
  const login = async (email: string, password: string): Promise<LoginResult> => {
    try {
      const signInResponse = await signIn({
        username: email,
        password,
        options: {
          // Use CUSTOM_WITH_SRP so Cognito runs PASSWORD_VERIFIER then CUSTOM_CHALLENGE
          authFlowType: 'CUSTOM_WITH_SRP',
          clientMetadata: {
            language: languageStore.currentLanguage,
          },
        },
      })
      if (signInResponse.isSignedIn) {
        await fetchAuthSession()

        const memberStatus = await logLoginMemberStatus()
        if (memberStatus.status === null) {
          await logout()
          throw new Error(memberStatus.error)
        } else if (memberStatus.status === 8 || memberStatus.status === 9) {
          suppressRedirect.value = true // リダイレクトを一時的に無効化
          try {
            await signOut()
          } catch (error) {
            console.error('SignOut error:', error)
          } finally {
            clearAuthData()
            suppressRedirect.value = false // フラグをリセット
          }
          throw new Error(t('LOGIN_ERROR_LOGIN_FAILED_BY_STATUS'))
        }

        // Log login history and check for backend errors (including tenant validation)
        const loginResult = await logLoginHistory()
        if (!loginResult.success && loginResult.error) {
          // Backend validation error - show error to user
          await logout()
          throw new Error(loginResult.error)
        }

        return {type: 'SUCCESS'}
      }

      const {signInStep} = signInResponse.nextStep
      switch (signInStep) {
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            type: 'NEW_PASSWORD_REQUIRED',
            message: t('LOGIN_ERROR_NEW_PASSWORD_REQUIRED'),
          }
        case 'CONFIRM_SIGN_UP':
          return {
            type: 'EMAIL_VERIFICATION_REQUIRED',
            message: t('LOGIN_ERROR_EMAIL_VERIFICATION_REQUIRED'),
          }
        case 'CONFIRM_SIGN_IN_WITH_CUSTOM_CHALLENGE':
        case 'CONFIRM_SIGN_IN_WITH_TOTP_CODE':
        case 'CONTINUE_SIGN_IN_WITH_TOTP_SETUP':
        case 'CONFIRM_SIGN_IN_WITH_SMS_CODE':
          return {
            type: 'NEED_OTP',
            message: t('LOGIN_ERROR_EMAIL_CODE_REQUIRED'),
          }
        default:
          return {
            type: 'ERROR',
            message: `${t('LOGIN_ERROR_UNSUPPORTED_STEP')} ${signInStep}`,
          }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      throw new Error(errorToMessage(error))
    }
  }

  /**
   * Confirms the OTP code during login.
   * @param code
   */
  const confirmOtp = async (code: string): Promise<LoginResult> => {
    try {
      const res = await confirmSignIn({
        challengeResponse: code,
        options: {
          clientMetadata: {
            language: languageStore.currentLanguage,
          },
        } as any,
      })
      if (res.isSignedIn) {
        await fetchAuthSession()
        const loginResult = await logLoginHistory()
        if (!loginResult.success && loginResult.error) {
          await logout()
          throw new Error(loginResult.error)
        }
        return {type: 'SUCCESS'}
      }
      return {type: 'ERROR', message: t('LOGIN_ERROR_INVALID_OTP_CODE')}
    } catch (error: any) {
      console.error('Confirm OTP error:', error)
      throw new Error(otpErrorToMessage(error))
    }
  }

  // Resend OTP by re-invoking the custom challenge with clientMetadata flag.
  // We intentionally pass a dummy code and mark action=RESEND_OTP so the Lambda
  // generates and emails a new code without penalizing failed attempts.
  /**
   *
   */
  const resendOtp = async (): Promise<void> => {
    try {
      await confirmSignIn({
        challengeResponse: '000000',
        options: {
          clientMetadata: {
            action: 'RESEND_OTP',
            language: languageStore.currentLanguage,
          },
        } as any,
      })
    } catch (error: any) {
      console.error('Resend OTP error:', error)
      throw new Error(otpErrorToMessage(error))
    }
  }

  /**
   * Initiates forgot password flow using custom Lambda function
   * @param email - User's email address
   */
  const forgotPassword = async (email: string): Promise<{type: string; message?: string}> => {
    try {
      // Import API_PATH and useApi dynamically to avoid circular dependencies
      const {API_PATH} = await import('@/defined/const')
      const useApi = (await import('@/composables/useApi')).default
      const {apiExecute} = useApi()

      const result = await apiExecute(API_PATH.REISSUE_PASSWORD, {
        email: email,
        languageCode: languageStore.currentLanguage || 'ja',
        validateFlag: false,
      })

      console.log('Forgot password initiated via Lambda:', result)
      return {
        type: 'SUCCESS',
        message: result.message || t('FORGOT_PASSWORD_CODE_SENT'),
      }
    } catch (error: any) {
      console.error('Forgot password error:', error)

      // Handle API response errors
      if (error.response?.data) {
        const errorData = error.response.data

        // Handle specific error types from Lambda function
        if (errorData.name === 'UserNotFound' || errorData.status === 404) {
          return {
            type: 'ERROR',
            message: t('FORGOT_PASSWORD_USER_NOT_FOUND'),
          }
        } else if (errorData.name === 'TooManyRequests' || errorData.status === 429) {
          return {
            type: 'ERROR',
            message: t('FORGOT_PASSWORD_LIMIT_EXCEEDED'),
          }
        } else if (errorData.name === 'InvalidParameter' || errorData.status === 400) {
          return {
            type: 'ERROR',
            message: t('FORGOT_PASSWORD_INVALID_PARAMETER'),
          }
        }

        return {
          type: 'ERROR',
          message: errorData.message || t('FORGOT_PASSWORD_UNKNOWN_ERROR'),
        }
      }

      return {
        type: 'ERROR',
        message: error.message || t('FORGOT_PASSWORD_UNKNOWN_ERROR'),
      }
    }
  }

  /**
   * Validates OTP code only without resetting password
   * Uses an intentionally invalid password to trigger validation
   * @param email - User's email address
   * @param confirmationCode - OTP code received via email
   */
  const validateOtpOnly = async (
    email: string,
    confirmationCode: string
  ): Promise<{type: string; message?: string}> => {
    try {
      // Use an intentionally invalid password to validate OTP without actually resetting
      await confirmResetPassword({
        username: email,
        confirmationCode,
        newPassword: 'xxxxyyyy@123', // Intentionally invalid password
        options: {
          clientMetadata: {
            language: languageStore.currentLanguage,
          },
        },
      })

      console.log('OTP validation succeeded, continue to reset password')

      return {
        type: 'SUCCESS',
        message: t('PASSWORD_RESET_OTP_VALID'),
      }
    } catch (error: any) {
      console.error('OTP validation error:', error)

      // InvalidPasswordException means OTP is valid but password is invalid (expected)
      if (error.name === 'InvalidPasswordException') {
        console.log('OTP is valid (password was intentionally invalid)')
        return {
          type: 'SUCCESS',
          message: t('PASSWORD_RESET_OTP_VALID'),
        }
      }

      // Handle OTP-specific errors
      if (error.name === 'CodeMismatchException') {
        return {
          type: 'ERROR',
          message: t('PASSWORD_RESET_OTP_INVALID_CODE'),
        }
      } else if (error.name === 'ExpiredCodeException') {
        return {
          type: 'ERROR',
          message: t('PASSWORD_RESET_OTP_EXPIRED_CODE'),
        }
      } else if (error.name === 'LimitExceededException') {
        return {
          type: 'ERROR',
          message: t('FORGOT_PASSWORD_LIMIT_EXCEEDED'),
        }
      } else if (error.name === 'InvalidPasswordException') {
        return {
          type: 'ERROR',
          message: t('FORGOT_PASSWORD_INVALID_PASSWORD'),
        }
      }

      return {
        type: 'ERROR',
        message: error.message || t('PASSWORD_RESET_OTP_INVALID_CODE'),
      }
    }
  }

  /**
   * Confirms forgot password with OTP code and new password
   * @param email - User's email address
   * @param confirmationCode - OTP code received via email
   * @param newPassword - New password to set
   */
  const confirmForgotPassword = async (
    email: string,
    confirmationCode: string,
    newPassword: string
  ): Promise<{type: string; message?: string}> => {
    try {
      await confirmResetPassword({
        username: email,
        confirmationCode,
        newPassword,
        options: {
          clientMetadata: {
            language: languageStore.currentLanguage,
          },
        },
      })

      console.log('Password reset confirmed successfully')
      return {
        type: 'SUCCESS',
        message: t('FORGOT_PASSWORD_RESET_SUCCESS'),
      }
    } catch (error: any) {
      console.error('Confirm forgot password error:', error)

      // Handle specific Cognito errors
      if (error.name === 'CodeMismatchException') {
        return {
          type: 'ERROR',
          message: t('FORGOT_PASSWORD_INVALID_CODE'),
        }
      } else if (error.name === 'ExpiredCodeException') {
        return {
          type: 'ERROR',
          message: t('FORGOT_PASSWORD_EXPIRED_CODE'),
        }
      } else if (error.name === 'InvalidPasswordException') {
        return {
          type: 'ERROR',
          message: t('FORGOT_PASSWORD_INVALID_PASSWORD'),
        }
      } else if (error.name === 'LimitExceededException') {
        return {
          type: 'ERROR',
          message: t('FORGOT_PASSWORD_LIMIT_EXCEEDED'),
        }
      }

      return {
        type: 'ERROR',
        message: error.message || t('FORGOT_PASSWORD_UNKNOWN_ERROR'),
      }
    }
  }

  const clearAuthData = (): void => {
    user.value = null
    idToken.value = null
    accessToken.value = null
    memberInfo.value = null
  }

  /**
   *
   */
  const logout = async (): Promise<void> => {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuthData()
      window.location.href = '/login'
    }
  }

  Hub.listen('auth', ({payload: {event}}) => {
    switch (event) {
      case 'signedIn':
        fetchAuthSession()
        break
      case 'signedOut':
      case 'tokenRefresh_failure':
        clearAuthData()
        if (!suppressRedirect.value) {
          window.location.href = '/login'
        }
        break
      default:
        break
    }
  })

  return {
    user,
    idToken,
    accessToken,
    // memberInfo, 今、使わないのでコメントアウトする
    isAuthenticated,
    fetchAuthSession,
    login,
    confirmOtp,
    resendOtp,
    forgotPassword,
    validateOtpOnly,
    confirmForgotPassword,
    logout,
  }
})
