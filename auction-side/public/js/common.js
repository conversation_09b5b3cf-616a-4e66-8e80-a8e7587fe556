// // viewport処理
// const isSp = window.matchMedia('(max-width: 767px)')
// $(window).on('load resize', () => {
//   if (isSp.matches) {
//     $("meta[name='viewport']").attr('content', 'width=device-width,initial-scale=1')
//   }
// })

// picture要素 IE対策
document.createElement('picture')

// // ハンバーガーメニュー
// $(() => {
//   $('header p.btnMenu').click(() => {
//     $('header .gNav').slideToggle()
//     $('header p.btnMenu').toggleClass('close')
//   })
// })
// // ハンバーガーメニュースクロール後固定

// // SPナビ 入れ子の開閉
// $(() => {
//   $('header .gNav nav ul.only_sp li p').click(function () {
//     $(this).next('ul').slideToggle()
//     $(this).toggleClass('close')
//   })
// })
// $(() => {
//   $('footer nav .fNav_sp > ul > li > p').click(function () {
//     $(this).next('ul,dl').slideToggle()
//     $(this).toggleClass('close')
//   })
// })

// スムーススクロール
$(document).ready(() => {
  const urlHash = location.hash
  if (urlHash) {
    $('body,html').stop().scrollTop(0)
    setTimeout(() => {
      scrollToAnker(urlHash)
    }, 100)
  }
  $('a[href^="#"]').click(function () {
    const href = $(this).attr('href')
    const hash = href == '#' || href == '' ? 'html' : href
    scrollToAnker(hash)
    return false
  })

  /**
   *
   * @param hash
   */
  function scrollToAnker(hash) {
    const target = $(hash)
    const position = target.offset().top
    $('body,html').stop().animate({scrollTop: position}, 500)
  }
})

//　ヘッダー・フッター
// ログイン後
/**
 *
 */
function headerin() {
  $.ajax({
    url: '/inc/header_in.html',
    cache: false,
    async: false,
    dataType: 'html',
    success(html) {
      document.write(html)
    },
  })
}
// ログイン前
/**
 *
 */
function headerout() {
  $.ajax({
    url: '/inc/header_out.html',
    cache: false,
    async: false,
    dataType: 'html',
    success(html) {
      document.write(html)
    },
  })
}
/**
 *
 */
function footer() {
  $.ajax({
    url: '/inc/footer.html',
    cache: false,
    async: false,
    dataType: 'html',
    success(html) {
      document.write(html)
    },
  })
}

// ユーザーエージェント判別
const ua = navigator.userAgent.toLowerCase()
const ver = navigator.appVersion.toLowerCase()

const isMSIE = ua.indexOf('msie') > -1 && ua.indexOf('opera') == -1
const isIE6 = isMSIE && ver.indexOf('msie 6.') > -1
const isIE7 = isMSIE && ver.indexOf('msie 7.') > -1
const isIE8 = isMSIE && ver.indexOf('msie 8.') > -1
const isIE9 = isMSIE && ver.indexOf('msie 9.') > -1
const isIE10 = isMSIE && ver.indexOf('msie 10.') > -1
const isIE11 = ua.indexOf('trident/7') > -1
const isIE = isMSIE || isIE11
const isEdge = ua.indexOf('edge') > -1
const isChrome = ua.indexOf('chrome') > -1 && ua.indexOf('edge') == -1
const isFirefox = ua.indexOf('firefox') > -1
const isSafari = ua.indexOf('safari') > -1 && ua.indexOf('chrome') == -1
const isOpera = ua.indexOf('opera') > -1

$(() => {
  if (isOpera) {
    $('body').addClass('js_isOpera')
  } else if (isIE) {
    $('body').addClass('js_isIe')
  } else if (isChrome) {
    $('body').addClass('js_isChrome')
  } else if (isSafari) {
    $('body').addClass('js_isSafari')
  } else if (isEdge) {
    $('body').addClass('js_isEdge')
  } else if (isFirefox) {
    $('body').addClass('js_isFirefox')
  } else {
    return false
  }
})

const _ua = (function (u) {
  return {
    Tablet:
      (u.indexOf('windows') != -1 && u.indexOf('touch') != -1 && u.indexOf('tablet pc') == -1) ||
      u.indexOf('ipad') != -1 ||
      (u.indexOf('android') != -1 && u.indexOf('mobile') == -1) ||
      (u.indexOf('firefox') != -1 && u.indexOf('tablet') != -1) ||
      u.indexOf('kindle') != -1 ||
      u.indexOf('silk') != -1 ||
      u.indexOf('playbook') != -1,
    Mobile:
      (u.indexOf('windows') != -1 && u.indexOf('phone') != -1) ||
      u.indexOf('iphone') != -1 ||
      u.indexOf('ipod') != -1 ||
      (u.indexOf('android') != -1 && u.indexOf('mobile') != -1) ||
      (u.indexOf('firefox') != -1 && u.indexOf('mobile') != -1) ||
      u.indexOf('blackberry') != -1,
  }
})(window.navigator.userAgent.toLowerCase())

$(() => {
  if (_ua.Mobile) {
    $('body').addClass('js_isMobile')
  } else if (_ua.Tablet) {
    $('body').addClass('js_isTablet')
  } else {
    $('body').addClass('js_isPc')
  }
})

if (navigator.platform.indexOf('Win') != -1) {
  $('body').addClass('js_isWin')
} else {
  $('body').addClass('js_isNotWin')
}

$(() => {
  if (ua.indexOf('iphone') > 0) {
    $('body').addClass('iphone')
  } else if (ua.indexOf('android') > 0 && ua.indexOf('mobile') > 0) {
    $('body').addClass('android')
  } else if (ua.indexOf('ipad') > 0) {
    $('body').addClass('ipad')
  }
})

// 高さ合わせ
$(() => {
  $('.matchH').matchHeight()
})
/* jquery-match-height 0.7.2 by @liabru
   http://brm.io/jquery-match-height/
   License MIT */
!(function (t) {
  'use strict'
  typeof define == 'function' && define.amd
    ? define(['jquery'], t)
    : typeof module != 'undefined' && module.exports
      ? (module.exports = t(require('jquery')))
      : t(jQuery)
})(t => {
  var e = -1,
    o = -1,
    n = function (t) {
      return parseFloat(t) || 0
    },
    a = function (e) {
      let o = 1,
        a = t(e),
        i = null,
        r = []
      return (
        a.each(function () {
          const e = t(this),
            a = e.offset().top - n(e.css('margin-top')),
            s = r.length > 0 ? r[r.length - 1] : null
          s === null
            ? r.push(e)
            : Math.floor(Math.abs(i - a)) <= o
              ? (r[r.length - 1] = s.add(e))
              : r.push(e),
            (i = a)
        }),
        r
      )
    },
    i = function (e) {
      const o = {
        byRow: !0,
        property: 'height',
        target: null,
        remove: !1,
      }
      return typeof e == 'object'
        ? t.extend(o, e)
        : (typeof e == 'boolean' ? (o.byRow = e) : e === 'remove' && (o.remove = !0), o)
    },
    r = (t.fn.matchHeight = function (e) {
      const o = i(e)
      if (o.remove) {
        const n = this
        return (
          this.css(o.property, ''),
          t.each(r._groups, (t, e) => {
            e.elements = e.elements.not(n)
          }),
          this
        )
      }
      return this.length <= 1 && !o.target
        ? this
        : (r._groups.push({elements: this, options: o}), r._apply(this, o), this)
    })
    ; (r.version = '0.7.2'),
      (r._groups = []),
      (r._throttle = 80),
      (r._maintainScroll = !1),
      (r._beforeUpdate = null),
      (r._afterUpdate = null),
      (r._rows = a),
      (r._parse = n),
      (r._parseOptions = i),
      (r._apply = function (e, o) {
        let s = i(o),
          h = t(e),
          l = [h],
          c = t(window).scrollTop(),
          p = t('html').outerHeight(!0),
          u = h.parents().filter(':hidden')
        return (
          u.each(function () {
            const e = t(this)
            e.data('style-cache', e.attr('style'))
          }),
          u.css('display', 'block'),
          s.byRow &&
          !s.target &&
          (h.each(function () {
            let e = t(this),
              o = e.css('display')
            o !== 'inline-block' && o !== 'flex' && o !== 'inline-flex' && (o = 'block'),
              e.data('style-cache', e.attr('style')),
              e.css({
                display: o,
                'padding-top': '0',
                'padding-bottom': '0',
                'margin-top': '0',
                'margin-bottom': '0',
                'border-top-width': '0',
                'border-bottom-width': '0',
                height: '100px',
                overflow: 'hidden',
              })
          }),
            (l = a(h)),
            h.each(function () {
              const e = t(this)
              e.attr('style', e.data('style-cache') || '')
            })),
          t.each(l, (e, o) => {
            let a = t(o),
              i = 0
            if (s.target) i = s.target.outerHeight(!1)
            else {
              if (s.byRow && a.length <= 1) return void a.css(s.property, '')
              a.each(function () {
                let e = t(this),
                  o = e.attr('style'),
                  n = e.css('display')
                n !== 'inline-block' && n !== 'flex' && n !== 'inline-flex' && (n = 'block')
                const a = {
                  display: n,
                }
                  ; (a[s.property] = ''),
                    e.css(a),
                    e.outerHeight(!1) > i && (i = e.outerHeight(!1)),
                    o ? e.attr('style', o) : e.css('display', '')
              })
            }
            a.each(function () {
              let e = t(this),
                o = 0
                ; (s.target && e.is(s.target)) ||
                  (e.css('box-sizing') !== 'border-box' &&
                    ((o += n(e.css('border-top-width')) + n(e.css('border-bottom-width'))),
                      (o += n(e.css('padding-top')) + n(e.css('padding-bottom')))),
                    e.css(s.property, `${i - o}px`))
            })
          }),
          u.each(function () {
            const e = t(this)
            e.attr('style', e.data('style-cache') || null)
          }),
          r._maintainScroll && t(window).scrollTop((c / p) * t('html').outerHeight(!0)),
          this
        )
      }),
      (r._applyDataApi = function () {
        const e = {}
        t('[data-match-height], [data-mh]').each(function () {
          const o = t(this),
            n = o.attr('data-mh') || o.attr('data-match-height')
          n in e ? (e[n] = e[n].add(o)) : (e[n] = o)
        }),
          t.each(e, function () {
            this.matchHeight(!0)
          })
      })
  const s = function (e) {
    r._beforeUpdate && r._beforeUpdate(e, r._groups),
      t.each(r._groups, function () {
        r._apply(this.elements, this.options)
      }),
      r._afterUpdate && r._afterUpdate(e, r._groups)
  }
    ; (r._update = function (n, a) {
      if (a && a.type === 'resize') {
        const i = t(window).width()
        if (i === e) return
        e = i
      }
      n
        ? o === -1 &&
        (o = setTimeout(() => {
          s(a), (o = -1)
        }, r._throttle))
        : s(a)
    }),
      t(r._applyDataApi)
  const h = t.fn.on ? 'on' : 'bind'
  t(window)[h]('load', t => {
    r._update(!1, t)
  }),
    t(window)[h]('resize orientationchange', t => {
      r._update(!0, t)
    })
})

// ///[共通]ページトップボタン
$(() => {
  const pageTop = $('#page_top')
  pageTop.hide()
  $(window).scroll(function () {
    if ($(this).scrollTop() > 100) {
      // 100pxスクロールしたら表示
      pageTop.fadeIn()
    } else {
      pageTop.fadeOut()
    }
  })
  pageTop.click(() => {
    $('body,html').animate(
      {
        scrollTop: 0,
      },
      500
    ) // 0.5秒かけてトップへ移動

    pageTop.fadeOut()
    // ボタンをクリックした後、すぐに非表示にする
    return false
  })

  // // フッター手前でストップ
  // $('#page_top').hide()
  // $(window).on('scroll', () => {
  //   const scrollHeight = $(document).height()
  //   const scrollPosition = $(window).height() + $(window).scrollTop()
  //   const footHeight = $('footer').innerHeight() - 20 // footHeightの値を減らしボタン位置調整
  //   // var footOffsetTop = $("footer").offset().top; TODO:　使わないようです、また、エラーになったため、一旦コメントアウトします。

  //   if ($(window).width() <= 768) {
  //     if (scrollPosition >= footOffsetTop) {
  //       $('#page_top').css({
  //         position: 'absolute',
  //         top: '-15px',
  //       })
  //     } else {
  //       $('#page_top').css({
  //         position: 'fixed',
  //         bottom: '15px',
  //         top: 'auto',
  //       })
  //     }
  //   } else if (scrollHeight - scrollPosition <= footHeight) {
  //     $('#page_top').css({
  //       position: 'absolute',
  //       bottom: footHeight,
  //     })
  //   } else {
  //     $('#page_top').css({
  //       position: 'fixed',
  //       bottom: '15px',
  //       top: 'auto',
  //     })
  //   }
  // })
})

// ///[商品一覧]表示並べ替えパネルボタン
$(() => {
  // ヘッダー条件検索開閉
  $('.menu_trigger').on('click', () => {
    const filterPanel = $('.sorting_panel')
    if (!filterPanel.hasClass('is-active')) {
      filterPanel.addClass('is-active')
    } else {
      filterPanel.removeClass('is-active')
    }
  })
  // ボタン以外のエリアクリックで閉じる
  $(document).on('click', e => {
    if (!$(e.target).closest('.sorting').length) {
      $('.sorting_panel').removeClass('is-active')
    }
  })
  // リストアイテムをクリックした際の動作
  $('.option_item').on('click', function () {
    const selectedText = $(this).text() // クリックされたリストのテキストを取得
    $('.option_selected').text(selectedText) // 選択されたテキストを表示している要素に反映
    $('.sorting_panel').removeClass('is-active')
  })
})

// ///[商品一覧]ディスプレイタイプ切り替え
$(() => {
  /**
   *
   * @param button
   * @param itemListClass
   */
  function toggleActiveClass(button, itemListClass) {
    $('.btn.row, .btn.panel').removeClass('is-active')
    button.addClass('is-active')
    $('.item-list').removeClass('row panel').addClass(itemListClass)
  }

  $('.btn.row').on('click', function () {
    toggleActiveClass($(this), 'row')
  })

  $('.btn.panel').on('click', function () {
    toggleActiveClass($(this), 'panel')
  })
})

// ///[QA]
$(() => {
  $('#ac-menu .label').on('click', function () {
    $(this).next().slideToggle()
    $(this).toggleClass('open')
  })
})

// jQuery category selection code removed - now handled by Vue component

// .nav-btn-searchのクリック時の制御
$(() => {
  $('.nav-btn-search').on('click', () => {
    const filterPanelSp = $('.filter-panel-sp')
    filterPanelSp.slideToggle()
  })

  $('.close-panel span').on('click', () => {
    $('.filter-panel-sp').slideUp()
  })
})


// ///[TOP]検索欄カテゴリをもっと見るボタン
$(() => {
  const $list = $('.list-category')
  const $btn = $('.btn.more-category')

  $btn.on('click', () => {
    const isExpanded = $list.hasClass('expanded')

    if (isExpanded) {
      // 閉じる：アニメーション完了後にテキスト変更
      $list.removeClass('expanded')
      $list.one('transitionend', () => {
        $btn.removeClass('expanded').find('span').text('もっと見る')
      })
    } else {
      // 展開：即時テキスト変更
      $list.addClass('expanded')
      $btn.addClass('expanded').find('span').text('閉じる')
    }
  })
})
// ///[TOP]おすすめ商品スライダー
$(() => {
  const $slider = $('.list-item-gallery.top')

  // Slick初期化
  $slider.slick({
    arrows: true,
    dots: true,
    infinite: true,
    slidesToShow: 5,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1081,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  })

  // 高さを揃える関数
  /**
   *
   */
  function equalizeSlideHeights() {
    let maxHeight = 0

    $slider.find('.slick-slide').each(function () {
      $(this).css('height', 'auto') // 高さリセット
      const h = $(this).outerHeight()
      if (h > maxHeight) maxHeight = h
    })

    $slider.find('.slick-slide').css('height', `${maxHeight}px`)
  }

  // 初期化直後・スライド変更時に高さを揃える
  $slider.on('setPosition', () => {
    equalizeSlideHeights()
  })

  // 画像読み込み後やリサイズ時にも高さ調整
  $(window).on('load resize', () => {
    setTimeout(equalizeSlideHeights, 300)
  })
})

$(() => {
  const $slider = $('.list-item-gallery.top')

  const initSliderIfEnoughItems = function () {
    const itemCount = $slider.find('li').length

    let slidesToShow = 5
    const windowWidth = window.innerWidth

    if (windowWidth <= 767) {
      slidesToShow = 2
    } else if (windowWidth <= 1080) {
      slidesToShow = 3
    }

    // すでにSlickが初期化されていたら一度破棄
    if ($slider.hasClass('slick-initialized')) {
      $slider.slick('unslick')
    }

    // アイテム数が表示数以上 → slick初期化
    if (itemCount > slidesToShow) {
      $slider.removeClass('not-slick').slick({
        arrows: true,
        dots: true,
        infinite: true,
        slidesToShow,
        slidesToScroll: 1,
        responsive: [
          {breakpoint: 1080, settings: {slidesToShow: 3}},
          {breakpoint: 767, settings: {slidesToShow: 2}},
        ],
      })
    } else {
      // 少ない場合は not-slick クラス付与
      $slider.addClass('not-slick')
    }
  }

  // 初回実行
  initSliderIfEnoughItems()

  // リサイズ時に再判定（オプション）
  let resizeTimer
  $(window).on('resize', () => {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(() => {
      initSliderIfEnoughItems()
    }, 300)
  })
})
