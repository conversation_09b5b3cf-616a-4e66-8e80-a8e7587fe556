const {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} = require('@aws-sdk/client-s3')
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner')
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const XlsxTemplate = require(`${process.env.COMMON_LAYER_PATH}xlsx-template.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

const s3 = new S3Client()

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  console.log(params)

  const base = new Base(pool, params.languageCode)
  const memberNo = authorizer.member_no
  let key = null

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(() =>
      pool.rlsQuery(
        authorizer.tenant_no,
        'SELECT * FROM "f_get_bid_payment"($1,$2,$3);',
        [authorizer.tenant_no, params.exhibitionItemNo, memberNo]
      )
    )
    .then(async result => {
      if (result.length === 0) {
        return Promise.reject('インボイス情報見つかりませんでした。')
      }
      const invoice = result[0]
      console.log('invoice: ', invoice)
      console.log('item_field: ', invoice.item_field)
      console.log('member_field: ', invoice.member_field)
      const date = new Date(invoice.create_datetime)
      const now = new Date()
      key = `auction/invoices/${authorizer.tenant_no}/pdf/${memberNo}-${date}-${invoice.order_no}-${base.randomString(10)}.pdf`
      let postage = 0
      let campaign = 0
      invoice.options?.map(x => {
        if (x.title.includes('送料')) {
          postage = x.price
        } else if (x.title.includes('キャンペーン')) {
          campaign = x.price
        }
      })

      // Get key_order_no
      const key_order_no = `${memberNo}-${Common.getFormatDate(date, 'YYYYMMDD')}-${invoice.order_no}`

      const input = {
        key_now: `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`,
        key_date: `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`,
        key_order_no,
        key_name: invoice.member_field.memberName,
        key_firmed_postcode: invoice.member_field?.postalCode || '',
        key_firmed_address: invoice.member_field?.companyAddress || '',
        key_firmed_name: invoice.member_field?.companyName || '',
        key_firmed_phone: `TEL：${invoice.member_field?.tel || ''}`,
        key_postcode: invoice.member_field?.postalCode || '',
        key_address: invoice.member_field?.companyAddress || '',
        key_phone: invoice.member_field?.tel || '',
        manage_no: invoice.manage_no,
        key_product_name: invoice.item_field.productName,
        key_item_price_with_tax: Math.round(
          invoice.bid_success_price +
            (invoice.bid_success_price * invoice.tax_rate) / 100
        ),
        key_item_price: invoice.bid_success_price,
        key_quantity: invoice.item_field.quantity,
        key_payment_method:
          invoice.payment_flag === 1
            ? '支払済み'
            : invoice.payment_flag === 2
              ? '支払処理中'
              : invoice.payment_flag === 3
                ? 'キャンセル'
                : '未払い',
        key_options_postage: Number(postage),
        key_options_campaign: Number(campaign),
        key_total_price_with_tax:
          Number(
            invoice.bid_success_price +
            (invoice.bid_success_price * invoice.tax_rate) / 100
          ) +
          Number(postage) +
          Number(campaign),
      }

      console.log('input: ', input)
      const template = 'invoice.xlsx'
      const xlsx = await XlsxTemplate.generate(template, input)
      console.log({xlsx})

      return Common.invokeLambda(process.env.CONVERT_XLSX_TO_PDF_LAMBDA_ARN, {
        body: {
          content: xlsx,
        },
      }).then(content => {
        console.log('content: ', content)
        return Promise.resolve(Buffer.from(content, 'base64'))
      })
    })
    .then(pdf => {
      console.log('pdf: ', pdf)
      const command = new PutObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: key,
        Body: pdf,
        ContentType: 'application/pdf',
        ACL: 'private',
      })
      console.log('command: ', command)
      return s3.send(command)
    })
    .then(result => {
      console.log('result', result)
      // get pre-signed url
      const expiresIn = Number.parseInt(process.env.SIGNED_URL_EXPIRES, 10)
      const getCommand = new GetObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: key,
      })
      return getSignedUrl(s3, getCommand, {expiresIn})
    })
    .then(url => {
      console.log({url})
      return {url}
    })
    .then(result => {
      return base.createSuccessResponse(cb, result, false)
    })
    .catch(error => {
      return base.createErrorResponse(cb, error, ctx)
    })
}
