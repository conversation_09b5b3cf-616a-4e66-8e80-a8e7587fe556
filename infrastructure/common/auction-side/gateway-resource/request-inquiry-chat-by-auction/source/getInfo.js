const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.PGHOST)

const getInfo = (tenant_no, language, exhibition_item_no, member_no, key_string) => {
  return Promise.all([
    pool.rlsQuery(
      tenant_no,
      'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
      [
        tenant_no,
        key_string,
        language,
      ]
    ),
    pool.rlsQuery(
      tenant_no,
      'SELECT * FROM "f_get_member_info_by_member_no"($1,$2);',
      [
        member_no,
        tenant_no,
      ]
    ),
    pool.rlsQuery(
      tenant_no,
      'SELECT * FROM "f_get_item_info"($1,$2,$3,$4);',
      [
        exhibition_item_no,
        tenant_no,
        language,
        member_no,
      ]
    ),
  ])
}

module.exports = getInfo
