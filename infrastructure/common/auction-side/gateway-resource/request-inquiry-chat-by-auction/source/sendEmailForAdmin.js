const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.PGHOST)
const getInfo = require('./getInfo')

const sendEmailForAdmin = (tenant_no, language, exhibition_item_no, member_no, message, paramsLanguageCode) => {
  const key_string = [
    'EMAIL_INQUIRY_REQUEST_FOR_ADMIN',
    'EMAIL_COMMON_FOOTER',
    'EMAIL_FROM'
  ]
  const base = new Base(pool, paramsLanguageCode)
  return getInfo(tenant_no, language, exhibition_item_no, member_no, key_string)
    .then(([constants, result_member, result_item]) => {
      const member = result_member?.length > 0 ? result_member[0] : null;
      if (!member) {
        throw {
          status: 400,
          message: base.define.message.E900001,
          detail: 'member not found',
        }
      }
      const item = result_item?.length > 0 ? result_item[0] : null;
      if (!item) {
        throw {
          status: 400,
          message: base.define.message.E900001,
          detail: 'item not found',
        };
      }

      const footer =
        constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') ||
        {};
      const mailFrom =
        constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
        null;
      const mail =
        constants.find(
          x => x.key_string === 'EMAIL_INQUIRY_REQUEST_FOR_ADMIN'
        ) || {};
      const title = mail.value1;
      const fields = mail.value5 ? mail.value5.split(',') : [];
      const sender = mailFrom
        ? `"${mailFrom}"<${mail.value2}>`
        : mail.value2;
      const receivers = mail.value3 ? mail.value3.split(',') : [];
      const bcc = [];

      const content_text = [
        fields[0] || '',
        item.free_field.product_name
          ? `${fields[1] || ''}: ${item.free_field.product_name}`
          : '',
      ].join('\n')

      const content = Common.format(mail.value4, [
        member?.free_field.memberName || '',
        member?.member_id || '',
        member?.free_field.companyName || '',
        member?.free_field.tel || '',
        member?.free_field.email || '',
        content_text,
        message,
      ]);

      // 受信者がいない場合はメール送信をスキップ
      if (receivers.length === 0) {
        console.log('No receivers found, skipping email send for admin');
        return Promise.resolve({message: 'Email skipped - no receivers'});
      }

      return Common.sendMailBySES(
        title,
        content,
        sender,
        receivers,
        bcc
      );
    })
    .catch(error => {
      console.error('Error occurred in sendEmailForAdmin:', error);
      throw error;
    });
}

module.exports = sendEmailForAdmin
