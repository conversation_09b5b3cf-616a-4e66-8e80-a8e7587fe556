const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const pool = new PgPool(process.env.PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  const admin_no = Base.extractAdminNo(e);
  const auctionDomainName = process.env.AUCTION_DOMAIN_NAME;

  let target_member_no = null;
  let email = null;
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log(params);

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      // Validation
      if (params.chat_message.length > 300) {
        const error = {
          status: 400,
          message: Define.MESSAGE.E000804
        }
        return Promise.reject(error)
      } else {
        return Promise.resolve()
      }
    })
    .then(() => {
      const sqlParams = [
        tenantNo,
        params.exhibition_message_no,
        admin_no
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.UPDATE_INQUIRY_CHAT_FUNCTION, sqlParams)
    })
    .then((result) => {
      target_member_no = result.length > 0 ? result[0].target_member_no : null;

      // member_noが取得できない場合は処理終了
      if (!target_member_no) {
        const error = {
          status: 400,
          message: Define.MESSAGE.E000805
        }
        return Promise.reject(error)
      }

      const sqlParams = [
        tenantNo,
        params.exhibition_item_no,
        params.exhibition_message_no,
        params.chat_message,
        admin_no
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.REGIST_INQUIRY_CHAT_FUNCTION, sqlParams)
    })
    .then(() => {
      // 会員情報の取得
      const sqlParams = [
        tenantNo,
        target_member_no,
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.GET_MEMBER_INFO_FOR_MEMBER_NO_FUNCTION, sqlParams)
    })
    .then((memberInfo) => {
      const member = memberInfo.length > 0 ? memberInfo[0] : null;
      if (!member) {
        const error = {
          status: 400,
          message: Define.MESSAGE.E000138
        }
        return Promise.reject(error)
      }

      // 言語、メールアドレスの取得
      const lang = member.free_field.lang ? member.free_field.lang : 'ja';
      email = member.free_field.email ? member.free_field.email : null;

      // 定数の取得
      const sqlParams = [
        ['EMAIL_INQUIRY_REPLY_TO_MEMBER', 'EMAIL_COMMON_FOOTER', 'EMAIL_FROM'],
        tenantNo,
        lang,
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.GET_CONSTANTS_BY_KEYS, sqlParams)
    })
    .then((constants) => {
      const footer =
        constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') ||
        {};
      const mailFrom =
        constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
        null;
      const mail =
        constants.find(
          x => x.key_string === 'EMAIL_INQUIRY_REPLY_TO_MEMBER'
        ) || {};
      const title = mail.value1;
      const sender = mailFrom
        ? `"${mailFrom}"<${mail.value2}>`
        : mail.value2;
      const receivers = email ? [email] : [];
      const bcc = mail.value3 ? mail.value3.split(',') : [];

      console.log('auctionDomainName', auctionDomainName);
      const content_text = `https://${auctionDomainName}/details/chat/${params.exhibition_item_no}`;

      const content = Common.format(mail.value4, [
        content_text,
        footer.value4 || '',
      ]);

      // 受信者がいない場合はメール送信をスキップ
      if (receivers.length === 0) {
        console.log('No receivers found, skipping email send for user');
        return Promise.resolve({message: 'Email skipped - no receivers'});
      }

      return Common.sendMailBySES(
        title,
        content,
        sender,
        receivers,
        bcc
      );
    })
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      return Base.createErrorResponse(cb, error);
    });
};
