CREATE OR REPLACE FUNCTION public.f_update_inquiry_chat_checked_admin (
    in_tenant_no bigint,
    in_exhibition_message_no bigint,
    in_admin_no bigint
)
RETURNS TABLE (
    target_member_no bigint
)
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 問合せチャットの回答者更新
-- Parameters
-- @param in_tenant_no
-- @param in_exhibition_message_no
-- @param in_admin_no
----------------------------------------------------------------------------------------------------

BEGIN

  UPDATE t_exhibition_message
     SET checked_admin_no = in_admin_no
   WHERE exhibition_message_no = in_exhibition_message_no
     AND tenant_no = in_tenant_no;

  RETURN QUERY
    SELECT TEM.member_no   -- target_member_no
      FROM t_exhibition_message TEM
    WHERE TEM.exhibition_message_no = in_exhibition_message_no
      AND TEM.tenant_no = in_tenant_no;

END;
$BODY$;
