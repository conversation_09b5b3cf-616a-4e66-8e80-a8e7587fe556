CREATE OR REPLACE FUNCTION public.f_get_member_info_for_member_no(
    in_tenant_no bigint,
    in_member_no bigint
)
RETURNS TABLE(
    member_no bigint,
    free_field jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- member_noから会員情報を取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_member_no bigint - 会員番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT mm.member_no AS member_no,
         mm.free_field AS free_field
    FROM
      m_member mm
  WHERE mm.tenant_no = in_tenant_no
    AND mm.member_no = in_member_no
    AND COALESCE(mm.delete_flag, 0) = 0;

END;

$BODY$;
