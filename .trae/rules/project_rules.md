# GMO SaaS Auction System - Project Rules

## Framework and Technology Stack

### 1. Frontend Frameworks
- **Admin Side**: Vue 3 with CoreUI framework
- **Auction Side**: Vue 3 with Vuetify 3.x
- **Build Tool**: Vite 5.x for both applications
- **State Management**: Pinia with persistedstate plugin
- **Router**: Vue Router 4.x

### 2. Backend and Infrastructure
- **Demo Server**: Express.js with Node.js (ES modules)
- **Cloud Platform**: AWS (Lambda, API Gateway, S3, RDS Aurora Serverless V2)
- **Infrastructure as Code**: Terraform 1.9.8
- **Database**: PostgreSQL with Row Level Security (RLS)

### 3. Testing Framework Requirements
- **Unit Testing**: Vitest 3.x with jsdom environment
- **E2E Testing**: Playwright for auction-side
- **Coverage**: Use @vitest/coverage-v8 for coverage reports
- **Test Location**: 
  - Unit tests: `src/**/__tests__/` or `*.test.js` alongside source files
  - E2E tests: `tests/e2e/` directory

## Development Environment Setup

### 4. Node.js and Package Management
- **Node.js Version**: 20.x (specified in engines)
- **npm Version**: 10.x minimum
- **Package Manager**: Use npm (not yarn or pnpm)

### 5. AWS Configuration
- **Profiles Required**: `saas-dev`, `saas-demo`, `saas-prod`
- **Authentication**: AWS SSO with proper profile configuration
- **Deployment**: Automated S3 sync for frontend builds

## Code Style and Formatting

### 6. ESLint Configuration
- **Version**: ESLint 9.x with flat config
- **Plugins**: Vue, JSDoc, Prettier integration
- **Rules**: Follow project's eslint.config.mjs
- **Ignored Paths**: node_modules, dist, auction-side/public/js/*

### 7. Prettier Configuration (Auction Side)
```json
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": false,
  "arrowParens": "avoid",
  "vueIndentScriptAndStyle": true
}
```

## Project Structure Rules

### 8. Directory Organization
- **admin-side/**: CoreUI-based admin interface
- **auction-side/**: Vuetify-based auction interface  
- **demo-server/**: Express.js backend for development
- **infrastructure/**: Terraform configurations by environment
- **dll/**: Database schema definitions
- **dml/**: Database seed data
- **docs/**: Project documentation

### 9. Environment Configuration
- **Development**: Use .env.development files
- **Demo**: Use .env.demo and .env.demo2 files
- **Production**: Use .env.production files
- **Ports**: Admin (3000), Auction (5173), Demo Server (configurable)

## Deployment and Build Process

### 10. Build Commands
- **Admin Dev**: `npm run dev-admin`
- **Auction Dev**: `npm run dev-auction`
- **Build**: Environment-specific build commands (build-dev, build-demo, build-prod)
- **Deploy**: Automated S3 sync with cache control headers

### 11. Database Management
- **Schema**: Use dll/ directory for table definitions
- **Data**: Use dml/ directory for initial data
- **RLS**: Row Level Security enabled (see dll/rls/)
- **Functions**: Database functions in dll/function/

## Security and Authentication

### 12. Authentication System
- **AWS Cognito**: Used for user authentication
- **JWT**: Token-based authentication
- **Multi-tenant**: Tenant-based data isolation with RLS

### 13. API Guidelines
- **AWS SDK**: Use @aws-sdk/client-s3 for S3 operations
- **Axios**: HTTP client for API calls
- **Error Handling**: Proper error boundaries and logging

## Dependencies and Libraries

### 14. Key Dependencies
- **UI Libraries**: CoreUI (@coreui/vue), Vuetify (vuetify)
- **Utilities**: Lodash, date-fns, crypto-js
- **AWS**: aws-amplify, @aws-sdk packages
- **Charts**: Chart.js with Vue wrappers
- **File Handling**: Specific to auction functionality

### 15. Development Dependencies
- **TypeScript**: Used in auction-side with vue-tsc
- **Sass**: SCSS preprocessing
- **Autoprefixer**: CSS vendor prefixing
- **Compression**: Vite plugin for build optimization

## Avoid Using These APIs/Patterns

### 16. Deprecated or Avoided Patterns
- **Vue 2 Options API**: Use Composition API instead
- **Vuex**: Use Pinia for state management
- **CommonJS**: Use ES modules (type: "module")
- **Webpack**: Use Vite for build tooling
- **Class Components**: Use script setup syntax

### 17. Security Restrictions
- **No hardcoded credentials**: Use environment variables
- **No client-side secrets**: Keep sensitive data server-side
- **Validate inputs**: Always sanitize user inputs
- **CORS**: Proper CORS configuration for API endpoints