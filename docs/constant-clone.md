# テナントクローン手順書

このドキュメントでは、テナント1からテナント2へのデータクローン手順について説明します。

## 概要

テナント1のデータをテナント2にクローンする際、以下の手順を実行する必要があります。
特に、メールアドレスの設定が正しく更新されていることを確認することが重要です。

## 実行手順

### 1. 基本データのクローン

以下のスクリプトを順番に実行してください：

1. `scripts/clone-constant.sql` - 定数マスタのクローン
2. `scripts/clone-field-with-mappings.sql` - フィールドマッピングのクローン
3. `scripts/clone-admin-role.sql` - 管理者権限のクローン

### 2. メールアドレスの更新

#### 問題点
`m_constant_localized`テーブルには、メールテンプレート用のメールアドレスが格納されています。
- テナント1では`<EMAIL>`の形式で保存されています
- クローン後、テナント2でも`saas-1`が残ってしまう問題があります

#### 解決手順

**Step 1: 確認クエリ** - 対象データの確認
```sql
-- テナント2に残っているテナント1のメールアドレスを確認
SELECT
    constant_localized_no,
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
FROM m_constant_localized
WHERE tenant_no = 2
  AND (
    value2 LIKE 'info@saas-1%'
    OR value3 LIKE 'info@saas-1%'
    OR value4 LIKE 'info@saas-1%'
    OR value5 LIKE 'info@saas-1%'
  )
ORDER BY constant_localized_no;
```

**Step 2: 更新クエリ** - メールアドレスの修正
```sql
-- テナント1のメールアドレスをテナント2用に更新
UPDATE m_constant_localized
SET
    value2 = CASE
        WHEN value2 LIKE 'info@saas-1%'
        THEN '<EMAIL>'
        ELSE value2
    END,
    value3 = CASE
        WHEN value3 LIKE 'info@saas-1%'
        THEN '<EMAIL>'
        ELSE value3
    END,
    value4 = CASE
        WHEN value4 LIKE 'info@saas-1%'
        THEN '<EMAIL>'
        ELSE value4
    END,
    value5 = CASE
        WHEN value5 LIKE 'info@saas-1%'
        THEN '<EMAIL>'
        ELSE value5
    END,
    update_datetime = now()
WHERE tenant_no = 2
  AND (
    value2 LIKE 'info@saas-1%'
    OR value3 LIKE 'info@saas-1%'
    OR value4 LIKE 'info@saas-1%'
    OR value5 LIKE 'info@saas-1%'
  );
```
