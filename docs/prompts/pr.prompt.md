# GitHub Copilot用 PR文自動生成プロンプト

このプロンプトは、GitHub Copilotを使用してコミット内容を自動解析し、包括的なPR説明を生成するためのものです。

## 🤖 GitHub Copilot による自動PR文生成手順

### 1. 前提条件の確認

**重要**: ベースブランチは常に **`origin/develop`** として差分を取得してください。
絶対に `develop` や `main` をベースにしないでください。

### 2. コミット履歴とファイル変更の自動解析

GitHub Copilotに以下の処理を依頼してください：

```bash
# 1. リモートの最新情報を取得
git fetch origin develop

# 2. コミット履歴を取得して解析
git log origin/develop..HEAD --oneline --decorate

# 3. 詳細な差分を取得
git diff origin/develop...HEAD --name-status

# 4. ファイル別の変更内容を詳細解析
git diff origin/develop...HEAD
```

**Copilotへの指示**:
「上記のgitコマンドの出力結果を解析し、以下の観点から変更内容を分類・整理してください：
- 新機能の追加
- バグ修正
- リファクタリング
- ドキュメント更新
- テストコードの変更
- インフラ・設定ファイルの変更
- データベーススキーマの変更」

### 3. 関連Issue の自動検索・マッピング

GitHub Copilotに以下を依頼：

```bash
# GitHub CLI を使用してIssueリストを取得
gh issue list --repo GMO-MAKESHOP/saas-mock --state open --limit 50 --json number,title,body
```

**Copilotへの指示**:
「コミットメッセージとファイル変更内容を基に、上記のIssueリストから関連するIssueを自動的に特定し、マッピングしてください。キーワードマッチング、ファイルパス、機能名などから関連性を判断してください。」

### 4. 影響範囲とリスク分析

**Copilotへの指示**:
「変更されたファイルとその内容を分析し、以下を自動判定してください：
- 影響を受けるコンポーネント・機能
- 潜在的なリスク（破壊的変更、パフォーマンス影響など）
- 必要なテスト項目
- デプロイ時の注意事項」

### 5. PR文の自動生成

以下のテンプレートを使用して、GitHub Copilotに自動生成を依頼：

**Copilotへの指示**:
「上記で解析した内容を基に、以下のテンプレートに従ってPR文を自動生成してください。ユーザーがコピペできるMarkdownコードブロックで出力してください。」

```markdown
# 関連Issue

{{AUTO_DETECTED_ISSUES}}
{{AUTO_DETECTED_ISSUES}}
{{AUTO_DETECTED_ISSUES}}　=> multiple issues can be listed

## 📝 このプルリクエストについて

{{AUTO_GENERATED_SUMMARY}}

## 🔧 変更内容

{{AUTO_CATEGORIZED_CHANGES}}

## 🎯 影響範囲

{{AUTO_DETECTED_IMPACT}}

## ⚠️ 破壊的変更・注意事項

{{AUTO_DETECTED_BREAKING_CHANGES}}

## ✅ チェックリスト

- [x] ビルドが正常に通ることを確認した
- [x] ESLintエラーがないことを確認した
- [ ] {{AUTO_GENERATED_TEST_ITEMS}}

## 📷 動作確認またはスクリーンショット

{{AUTO_SUGGESTED_TESTING_AREAS}}

## 🚀 デプロイ時の注意事項

{{AUTO_GENERATED_DEPLOY_NOTES}}

## 💬 補足事項

{{AUTO_DETECTED_ADDITIONAL_NOTES}}
```

### 6. 高度な自動分析機能

**A. コード品質チェック**
```bash
# ESLintチェック（変更ファイルのみ）
git diff origin/develop...HEAD --name-only --diff-filter=d | grep '\.(js|vue|ts)$' | xargs npx eslint

# テストカバレッジチェック
npm run test:coverage
```

**B. 依存関係影響分析**
```bash
# package.json の変更チェック
git diff origin/develop...HEAD -- package.json package-lock.json

# 新規・削除された依存関係の検出
```

**C. セキュリティチェック**
```bash
# セキュリティ脆弱性チェック
npm audit

# 機密情報の検出
git diff origin/develop...HEAD | grep -E '(password|secret|key|token)'
```

### 7. 継続的改善のためのフィードバック

**Copilotへの指示**:
「生成したPR文について、以下の観点から品質チェックを行い、改善提案をしてください：
- 技術的正確性
- 説明の明確性
- 不足している情報
- より良い表現方法」

## 🛠️ 使用例

```bash
# Copilotに以下を伝える：
"現在のブランチからorigin/developへのPRを作成したい。
上記のプロンプトに従って、コミット内容を自動解析し、包括的なPR説明を生成してください。"
```

## 📚 追加のCopilot活用ヒント

1. **コンテキスト提供**: プロジェクトの性質（SaaS、オークションシステム）を明示
2. **言語指定**: 日本語でのPR文生成を指定
3. **技術スタック**: Vue.js, Node.js, AWS等の技術情報を提供
4. **ビジネス要件**: GMO-MAKESHOPのビジネス要件に配慮した説明を依頼

## 🔄 従来形式のテンプレート（参考）

従来の手動生成用テンプレート：

```markdown
# 関連Issue

- #1

## 📝 このプルリクエストについて

- 概要:
  1. 変更内容1
  2. 変更内容2

## 🔧 変更内容

1. **機能名修正 (#XXX)**
   - 問題:
   - 対応:
   - 影響範囲:

## ✅ チェックリスト

- [x] ビルドが正常に通ることを確認した
- [x] 動作確認内容：
  - [ ] 1.
  - [ ] 2.

## 📷 動作確認またはスクリーンショット
```

## 🚀 実際の運用時のワークフロー

### Step 1: GitHub Copilotへの初期指示
```
"GMO-MAKESHOPのSaaSオークションシステムのPR文を自動生成してください。
現在のブランチ: [ブランチ名]
ベースブランチ: origin/develop
技術スタック: Vue.js, Node.js, AWS, PostgreSQL"
```

### Step 2: コミット解析の実行
```
"以下のコマンドの出力を解析してPR文を生成してください：
git log origin/develop..HEAD --oneline
git diff origin/develop...HEAD --name-status"
```

### Step 3: Issue連携の確認
```
"以下のIssue一覧から関連するものを特定してください：
gh issue list --repo GMO-MAKESHOP/saas-mock --state open --json number,title"
```

### Step 4: 最終確認と調整
```
"生成されたPR文をレビューし、不足している情報や改善点があれば指摘してください。"
```
